// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        // إصدارات المكتبات
        kotlin_version = '1.9.10'
        gradle_version = '8.1.2'
        google_services_version = '4.4.0'
        firebase_crashlytics_version = '2.9.9'
    }
    
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven { url 'https://jitpack.io' }
    }
    
    dependencies {
        classpath "com.android.tools.build:gradle:$gradle_version"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        
        // إضافة Firebase (اختيارية)
        // classpath "com.google.gms:google-services:$google_services_version"
        // classpath "com.google.firebase:firebase-crashlytics-gradle:$firebase_crashlytics_version"
        
        // إضافة مكتبات أخرى حسب الحاجة
        // classpath 'com.google.dagger:hilt-android-gradle-plugin:2.48'
    }
}

plugins {
    id 'com.android.application' version '8.1.2' apply false
    id 'com.android.library' version '8.1.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.10' apply false
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.google.com' }
        
        // إضافة مستودعات أخرى حسب الحاجة
        // maven { url 'https://oss.sonatype.org/content/repositories/snapshots/' }
    }
    
    // إعدادات عامة للمشروع
    configurations.all {
        resolutionStrategy {
            // فرض استخدام إصدارات محددة لتجنب التعارضات
            force 'androidx.core:core:1.12.0'
            force 'androidx.appcompat:appcompat:1.6.1'
            force 'com.google.android.material:material:1.10.0'
            
            // تجنب التحديثات التلقائية للمكتبات
            cacheDynamicVersionsFor 10, 'minutes'
            cacheChangingModulesFor 4, 'hours'
        }
    }
}

// مهام التنظيف
task clean(type: Delete) {
    delete rootProject.buildDir
}

// إعدادات إضافية للمشروع
ext {
    // إصدارات المكتبات المشتركة
    minSdkVersion = 21
    compileSdkVersion = 34
    targetSdkVersion = 34
    buildToolsVersion = '34.0.0'
    
    // إصدارات مكتبات AndroidX
    appCompatVersion = '1.6.1'
    materialVersion = '1.10.0'
    constraintLayoutVersion = '2.1.4'
    coreVersion = '1.12.0'
    
    // إصدارات مكتبات أخرى
    okhttpVersion = '4.12.0'
    glideVersion = '4.16.0'
    gsonVersion = '2.10.1'
    
    // إعدادات التوقيع (يجب تخصيصها)
    keystoreFile = 'keystore/release.keystore'
    keystorePassword = 'your_keystore_password'
    keyAlias = 'your_key_alias'
    keyPassword = 'your_key_password'
}

// مهام مخصصة للمشروع
task generateVersionInfo {
    doLast {
        def versionFile = new File(project.rootDir, 'version.properties')
        def properties = new Properties()
        
        if (versionFile.exists()) {
            versionFile.withInputStream { properties.load(it) }
        }
        
        def versionCode = (properties.getProperty('VERSION_CODE', '0') as Integer) + 1
        def versionName = properties.getProperty('VERSION_NAME', '2.0')
        def buildTime = new Date().format('yyyy-MM-dd HH:mm:ss')
        
        properties.setProperty('VERSION_CODE', versionCode.toString())
        properties.setProperty('VERSION_NAME', versionName)
        properties.setProperty('BUILD_TIME', buildTime)
        
        versionFile.withOutputStream { properties.store(it, 'Version Info') }
        
        println "Generated version info: Code=$versionCode, Name=$versionName, Time=$buildTime"
    }
}

// تشغيل مهمة إنشاء معلومات الإصدار قبل البناء
tasks.whenTaskAdded { task ->
    if (task.name == 'preBuild') {
        task.dependsOn generateVersionInfo
    }
}

// إعدادات Gradle Wrapper
wrapper {
    gradleVersion = '8.4'
    distributionType = Wrapper.DistributionType.ALL
}
