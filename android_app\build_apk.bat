@echo off
echo ========================================
echo    بناء تطبيق العقل المبدع - Android APK
echo ========================================
echo.

REM التحقق من وجود Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Java غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Java 8 أو أحدث
    pause
    exit /b 1
)

REM التحقق من وجود Android SDK
if not exist "local.properties" (
    echo تحذير: ملف local.properties غير موجود
    echo يرجى إنشاء الملف وتحديد مسار Android SDK
    echo مثال: sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk
    pause
)

echo جاري تنظيف المشروع...
call gradlew clean
if %errorlevel% neq 0 (
    echo خطأ في تنظيف المشروع
    pause
    exit /b 1
)

echo.
echo جاري بناء إصدار التطوير (Debug)...
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo خطأ في بناء إصدار التطوير
    pause
    exit /b 1
)

echo.
echo جاري بناء إصدار الإنتاج (Release)...
call gradlew assembleRelease
if %errorlevel% neq 0 (
    echo خطأ في بناء إصدار الإنتاج
    pause
    exit /b 1
)

echo.
echo ========================================
echo           تم البناء بنجاح!
echo ========================================
echo.

REM عرض مواقع ملفات APK
echo ملفات APK المُنشأة:
echo.

if exist "app\build\outputs\apk\debug\*.apk" (
    echo إصدار التطوير (Debug):
    for %%f in (app\build\outputs\apk\debug\*.apk) do (
        echo   %%f
    )
    echo.
)

if exist "app\build\outputs\apk\release\*.apk" (
    echo إصدار الإنتاج (Release):
    for %%f in (app\build\outputs\apk\release\*.apk) do (
        echo   %%f
    )
    echo.
)

REM إنشاء مجلد للملفات النهائية
if not exist "output" mkdir output

REM نسخ ملفات APK إلى مجلد output
echo جاري نسخ ملفات APK إلى مجلد output...
if exist "app\build\outputs\apk\debug\*.apk" (
    copy "app\build\outputs\apk\debug\*.apk" "output\" >nul
)
if exist "app\build\outputs\apk\release\*.apk" (
    copy "app\build\outputs\apk\release\*.apk" "output\" >nul
)

echo.
echo تم نسخ جميع ملفات APK إلى مجلد: output\
echo.

REM عرض معلومات إضافية
echo معلومات إضافية:
echo - لتثبيت التطبيق على الجهاز: adb install output\[اسم_الملف].apk
echo - لتوقيع APK للنشر: استخدم Android Studio أو jarsigner
echo - لتحسين الحجم: تأكد من تفعيل ProGuard في إصدار الإنتاج
echo.

echo ========================================
echo        تم الانتهاء من جميع العمليات
echo ========================================
echo.

REM فتح مجلد output
echo هل تريد فتح مجلد الملفات النهائية؟ (y/n)
set /p choice=
if /i "%choice%"=="y" (
    start explorer output
)

pause
