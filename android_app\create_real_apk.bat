@echo off
chcp 65001 >nul
echo ========================================
echo    إنشاء ملف APK حقيقي للعقل المبدع
echo ========================================
echo.

echo التحقق من متطلبات النظام...

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo ثم إعادة تشغيل هذا السكريبت
    pause
    exit /b 1
)

echo ✅ Node.js متوفر

REM التحقق من وجود Cordova
cordova --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 تثبيت Cordova...
    npm install -g cordova
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Cordova
        pause
        exit /b 1
    )
)

echo ✅ Cordova متوفر

REM إنشاء مجلد output إذا لم يكن موجود
if not exist "output" mkdir output

REM إنشاء مشروع Cordova جديد
echo.
echo 🚀 إنشاء مشروع Cordova...
cd output
if exist "AlAqlAlMubdea" rmdir /s /q "AlAqlAlMubdea"

cordova create AlAqlAlMubdea com.alaqalmubdea.app "العقل المبدع"
if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء مشروع Cordova
    pause
    exit /b 1
)

cd AlAqlAlMubdea

REM إضافة منصة Android
echo.
echo 📱 إضافة منصة Android...
cordova platform add android
if %errorlevel% neq 0 (
    echo ❌ فشل في إضافة منصة Android
    echo.
    echo تأكد من تثبيت:
    echo 1. Android Studio
    echo 2. Android SDK
    echo 3. Java JDK 8+
    pause
    exit /b 1
)

REM نسخ ملف HTML الأصلي
echo.
echo 📄 نسخ ملف HTML الأصلي...
if exist "..\..\app\src\main\assets\kfojo.html" (
    copy "..\..\app\src\main\assets\kfojo.html" "www\index.html" >nul
    echo ✅ تم نسخ ملف HTML
) else (
    echo ⚠️  لم يتم العثور على ملف HTML الأصلي
    echo سيتم استخدام الملف الافتراضي
)

REM تحديث config.xml
echo.
echo ⚙️  تحديث إعدادات التطبيق...
(
echo ^<?xml version='1.0' encoding='utf-8'?^>
echo ^<widget id="com.alaqalmubdea.app" version="2.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0"^>
echo     ^<name^>العقل المبدع^</name^>
echo     ^<description^>منصة شاملة للإبداع والتعلم^</description^>
echo     ^<author email="<EMAIL>" href="https://alaqalmubdea.com"^>فريق العقل المبدع^</author^>
echo     ^<content src="index.html" /^>
echo     ^<allow-intent href="http://*/*" /^>
echo     ^<allow-intent href="https://*/*" /^>
echo     ^<allow-intent href="tel:*" /^>
echo     ^<allow-intent href="sms:*" /^>
echo     ^<allow-intent href="mailto:*" /^>
echo     ^<allow-intent href="geo:*" /^>
echo     ^<platform name="android"^>
echo         ^<allow-intent href="market:*" /^>
echo         ^<preference name="Orientation" value="portrait" /^>
echo         ^<preference name="Fullscreen" value="false" /^>
echo         ^<preference name="SplashMaintainAspectRatio" value="true" /^>
echo         ^<preference name="SplashShowOnlyFirstTime" value="false" /^>
echo         ^<preference name="SplashScreen" value="screen" /^>
echo         ^<preference name="SplashScreenDelay" value="4000" /^>
echo         ^<icon density="ldpi" src="www/res/icon/android/drawable-ldpi-icon.png" /^>
echo         ^<icon density="mdpi" src="www/res/icon/android/drawable-mdpi-icon.png" /^>
echo         ^<icon density="hdpi" src="www/res/icon/android/drawable-hdpi-icon.png" /^>
echo         ^<icon density="xhdpi" src="www/res/icon/android/drawable-xhdpi-icon.png" /^>
echo         ^<icon density="xxhdpi" src="www/res/icon/android/drawable-xxhdpi-icon.png" /^>
echo         ^<icon density="xxxhdpi" src="www/res/icon/android/drawable-xxxhdpi-icon.png" /^>
echo     ^</platform^>
echo     ^<access origin="*" /^>
echo     ^<allow-navigation href="*" /^>
echo ^</widget^>
) > config.xml

REM بناء APK
echo.
echo 🔨 بناء ملف APK...
echo هذا قد يستغرق عدة دقائق...
cordova build android
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء APK
    echo.
    echo تأكد من:
    echo 1. تثبيت Android Studio بشكل صحيح
    echo 2. تحديد متغيرات البيئة ANDROID_HOME و JAVA_HOME
    echo 3. قبول تراخيص Android SDK
    pause
    exit /b 1
)

REM نسخ APK إلى مجلد output الرئيسي
echo.
echo 📦 نسخ ملف APK...
if exist "platforms\android\app\build\outputs\apk\debug\app-debug.apk" (
    copy "platforms\android\app\build\outputs\apk\debug\app-debug.apk" "..\AlAqlAlMubdea_v2.0_Real.apk" >nul
    echo ✅ تم نسخ ملف APK إلى: output\AlAqlAlMubdea_v2.0_Real.apk
) else (
    echo ❌ لم يتم العثور على ملف APK المُنشأ
)

cd ..\..

echo.
echo ========================================
echo           تم إنشاء APK حقيقي!
echo ========================================
echo.

if exist "output\AlAqlAlMubdea_v2.0_Real.apk" (
    echo 🎉 تم إنشاء ملف APK حقيقي بنجاح!
    echo.
    echo 📁 الموقع: output\AlAqlAlMubdea_v2.0_Real.apk
    for %%f in (output\AlAqlAlMubdea_v2.0_Real.apk) do echo 📦 الحجم: %%~zf bytes
    echo.
    echo 🚀 يمكنك الآن تثبيت هذا الملف على أي جهاز Android!
    echo.
    echo 📋 خطوات التثبيت:
    echo 1. انسخ الملف إلى جهاز Android
    echo 2. فعل "مصادر غير معروفة" في الإعدادات
    echo 3. اضغط على الملف واختر "تثبيت"
) else (
    echo ❌ لم يتم إنشاء ملف APK
    echo راجع الأخطاء أعلاه وحاول مرة أخرى
)

echo.
pause
