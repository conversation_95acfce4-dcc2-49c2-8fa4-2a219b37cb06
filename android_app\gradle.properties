# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true

# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official

# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true

# Enable Gradle Build Cache
org.gradle.caching=true

# Enable Configuration Cache
org.gradle.configuration-cache=true

# Enable File System Watching
org.gradle.vfs.watch=true

# Disable unnecessary features for faster builds
android.enableJetifier=false
android.enableR8.fullMode=true

# ProGuard optimizations
android.enableR8=true

# Disable build features not needed
android.defaults.buildfeatures.buildconfig=false
android.defaults.buildfeatures.aidl=false
android.defaults.buildfeatures.renderscript=false
android.defaults.buildfeatures.resvalues=false
android.defaults.buildfeatures.shaders=false

# Performance optimizations
org.gradle.daemon=true
org.gradle.configureondemand=true

# Memory optimizations
org.gradle.workers.max=4

# Disable unnecessary warnings
android.suppressUnsupportedCompileSdk=34

# Enable incremental annotation processing
kapt.incremental.apt=true
kapt.use.worker.api=true

# Enable incremental compilation
kotlin.incremental=true
kotlin.incremental.useClasspathSnapshot=true

# Compiler optimizations
kotlin.compiler.execution.strategy=in-process

# Build optimizations
android.experimental.enableSourceSetPathsMap=true
android.experimental.enableParallelJsonSubsetExtraction=true

# Signing configurations (should be customized)
# RELEASE_STORE_FILE=keystore/release.keystore
# RELEASE_STORE_PASSWORD=your_store_password
# RELEASE_KEY_ALIAS=your_key_alias
# RELEASE_KEY_PASSWORD=your_key_password

# App-specific properties
APP_NAME=العقل المبدع
APP_VERSION_NAME=2.0
APP_VERSION_CODE=1
APP_PACKAGE_NAME=com.alaqalmubdea.app

# Build environment
BUILD_ENVIRONMENT=production
DEBUG_MODE=false

# Network configurations
NETWORK_TIMEOUT=30000
CONNECT_TIMEOUT=15000
READ_TIMEOUT=30000

# Cache configurations
CACHE_SIZE=50MB
CACHE_MAX_AGE=7
CACHE_MAX_STALE=30

# WebView configurations
WEBVIEW_DEBUGGING=false
WEBVIEW_CACHE_MODE=LOAD_DEFAULT
WEBVIEW_MIXED_CONTENT=COMPATIBILITY_MODE

# Security configurations
ENABLE_SECURITY_PROVIDER=true
ENABLE_CERTIFICATE_PINNING=false
ENABLE_OBFUSCATION=true

# Analytics configurations (if using Firebase)
# ENABLE_ANALYTICS=true
# ENABLE_CRASHLYTICS=true
# ANALYTICS_DEBUG=false

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_MEMORY_MONITORING=true
ENABLE_NETWORK_MONITORING=true

# Localization
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=ar,en
RTL_SUPPORT=true

# UI configurations
ENABLE_DARK_MODE=true
ENABLE_ANIMATIONS=true
ANIMATION_DURATION=300

# Storage configurations
ENABLE_ENCRYPTION=true
DATABASE_VERSION=1
SHARED_PREFS_NAME=AlAqlAlMubdea

# Notification configurations
ENABLE_NOTIFICATIONS=true
NOTIFICATION_CHANNEL_ID=alaqalmubdea_channel
NOTIFICATION_CHANNEL_NAME=العقل المبدع

# Update configurations
ENABLE_AUTO_UPDATE=false
UPDATE_CHECK_INTERVAL=24
FORCE_UPDATE_VERSION=1

# Backup configurations
ENABLE_BACKUP=true
BACKUP_INTERVAL=7
MAX_BACKUP_FILES=5

# Development configurations
ENABLE_LOGGING=true
LOG_LEVEL=INFO
ENABLE_DEBUG_MENU=false

# Testing configurations
ENABLE_TEST_MODE=false
MOCK_NETWORK_RESPONSES=false
TEST_USER_ID=test_user

# Feature flags
ENABLE_RECIPES=true
ENABLE_PROJECTS=true
ENABLE_EDUCATION=true
ENABLE_HEALTH=true
ENABLE_STORIES=true
ENABLE_DEALINGS=true

# API configurations
API_BASE_URL=https://api.alaqalmubdea.com
API_VERSION=v1
API_TIMEOUT=30000

# Content configurations
MAX_SEARCH_RESULTS=4
AUTO_SAVE_INTERVAL=30
CONTENT_CACHE_SIZE=100MB

# Social sharing
ENABLE_SHARING=true
SHARE_APP_NAME=العقل المبدع
SHARE_APP_URL=https://alaqalmubdea.com
