<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8b9b7eaf-204f-48ba-b7d8-0504cb75c8e4" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 1
}]]></component>
  <component name="ProjectId" id="2zPSYGHzJCxiYVvy0CTZCprtbfh" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.Unnamed.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "android.gradle.sync.needed": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "dart.analysis.tool.window.visible": "false",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/العقل المبدع/android_app",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="Unnamed" type="Application" factoryName="Application" nameIsGenerated="true">
      <module name="android_app" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8b9b7eaf-204f-48ba-b7d8-0504cb75c8e4" name="Changes" comment="" />
      <created>1751631265094</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751631265094</updated>
    </task>
    <servers />
  </component>
</project>