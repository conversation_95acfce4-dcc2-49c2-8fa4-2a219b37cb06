@echo off
echo ========================================
echo     نسخ المحتوى الأصلي إلى التطبيق
echo ========================================
echo.

REM التحقق من وجود الملف الأصلي
if not exist "..\kfojo.html" (
    echo خطأ: لم يتم العثور على الملف الأصلي kfojo.html
    echo يرجى التأكد من وجود الملف في المجلد الرئيسي
    pause
    exit /b 1
)

echo تم العثور على الملف الأصلي: ..\kfojo.html
echo.

REM إنشاء نسخة احتياطية من الملف الحالي
if exist "app\src\main\assets\kfojo.html" (
    echo إنشاء نسخة احتياطية من الملف الحالي...
    copy "app\src\main\assets\kfojo.html" "app\src\main\assets\kfojo_backup.html" >nul
    echo تم إنشاء نسخة احتياطية: kfojo_backup.html
    echo.
)

REM نسخ الملف الأصلي
echo جاري نسخ المحتوى الأصلي...
copy "..\kfojo.html" "app\src\main\assets\kfojo.html" >nul

if %errorlevel% equ 0 (
    echo ✓ تم نسخ المحتوى الأصلي بنجاح!
) else (
    echo ✗ فشل في نسخ المحتوى الأصلي
    pause
    exit /b 1
)

echo.
echo ========================================
echo           تم النسخ بنجاح!
echo ========================================
echo.

echo الآن يمكنك:
echo 1. بناء التطبيق باستخدام: build_apk.bat
echo 2. أو فتح المشروع في Android Studio
echo.

echo ملاحظة: تم الاحتفاظ بنسخة احتياطية من الملف السابق
echo في: app\src\main\assets\kfojo_backup.html
echo.

pause
