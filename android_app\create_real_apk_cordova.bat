@echo off
chcp 65001 >nul
echo ========================================
echo    إنشاء APK حقيقي باستخدام Cordova
echo ========================================
echo.

REM التحقق من Node.js
echo التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo ثم إعادة تشغيل هذا السكريبت
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

REM تثبيت Cordova
echo.
echo تثبيت Cordova...
call npm install -g cordova
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت Cordova
    pause
    exit /b 1
)
echo ✅ Cordova مثبت

REM إنشاء مجلد output
if not exist "output" mkdir output
cd output

REM حذف المشروع السابق إن وجد
if exist "AlAqlAlMubdea" rmdir /s /q "AlAqlAlMubdea"

REM إنشاء مشروع Cordova
echo.
echo إنشاء مشروع Cordova...
call cordova create AlAqlAlMubdea com.alaqalmubdea.app "العقل المبدع"
if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء مشروع Cordova
    pause
    exit /b 1
)

cd AlAqlAlMubdea

REM نسخ ملف HTML الأصلي
echo.
echo نسخ ملف HTML...
if exist "..\..\kfojo.html" (
    copy "..\..\kfojo.html" "www\index.html" >nul
    echo ✅ تم نسخ ملف HTML الأصلي
) else if exist "..\..\..\kfojo.html" (
    copy "..\..\..\kfojo.html" "www\index.html" >nul
    echo ✅ تم نسخ ملف HTML الأصلي
) else (
    echo ⚠️ لم يتم العثور على ملف HTML، سيتم استخدام الافتراضي
)

REM إضافة منصة Android
echo.
echo إضافة منصة Android...
call cordova platform add android
if %errorlevel% neq 0 (
    echo ❌ فشل في إضافة منصة Android
    echo.
    echo تأكد من تثبيت:
    echo 1. Android Studio
    echo 2. Android SDK
    echo 3. Java JDK 8+
    echo 4. تحديد متغيرات البيئة ANDROID_HOME و JAVA_HOME
    pause
    exit /b 1
)

REM بناء APK
echo.
echo بناء APK حقيقي...
echo هذا قد يستغرق عدة دقائق...
call cordova build android
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء APK
    echo.
    echo راجع الأخطاء أعلاه وتأكد من:
    echo 1. تثبيت Android Studio بشكل صحيح
    echo 2. تحديد متغيرات البيئة
    echo 3. قبول تراخيص Android SDK
    pause
    exit /b 1
)

REM نسخ APK إلى مجلد output
echo.
echo نسخ APK...
if exist "platforms\android\app\build\outputs\apk\debug\app-debug.apk" (
    copy "platforms\android\app\build\outputs\apk\debug\app-debug.apk" "..\AlAqlAlMubdea_REAL_CORDOVA.apk" >nul
    echo ✅ تم إنشاء APK حقيقي: output\AlAqlAlMubdea_REAL_CORDOVA.apk
    
    REM عرض معلومات الملف
    for %%f in (..\AlAqlAlMubdea_REAL_CORDOVA.apk) do (
        echo 📦 حجم الملف: %%~zf bytes
        set /a size_mb=%%~zf/1024/1024
        echo 📦 الحجم بالميجابايت: !size_mb! MB تقريباً
    )
) else (
    echo ❌ لم يتم العثور على APK المُنشأ
    echo راجع مجلد platforms\android\app\build\outputs\apk\ للتحقق
)

cd ..\..

echo.
echo ========================================
echo           تم إنشاء APK حقيقي!
echo ========================================
echo.

if exist "output\AlAqlAlMubdea_REAL_CORDOVA.apk" (
    echo 🎉 تم إنشاء APK حقيقي بنجاح!
    echo.
    echo 📁 الموقع: output\AlAqlAlMubdea_REAL_CORDOVA.apk
    echo 📱 هذا APK حقيقي مُنشأ بواسطة Cordova
    echo 🔧 يحتوي على جميع ميزات Android الأصلية
    echo.
    echo 🚀 خطوات التثبيت:
    echo 1. انسخ الملف إلى جهاز Android
    echo 2. فعل "مصادر غير معروفة" في الإعدادات
    echo 3. اضغط على الملف واختر "تثبيت"
    echo 4. امنح الأذونات المطلوبة
    echo.
    echo ✅ هذا APK أصلي وحقيقي 100%%
) else (
    echo ❌ لم يتم إنشاء APK
    echo راجع الأخطاء أعلاه وحاول مرة أخرى
)

echo.
pause
