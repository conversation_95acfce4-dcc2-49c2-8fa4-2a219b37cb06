@echo off
chcp 65001 >nul
echo ========================================
echo    إنشاء ملف APK للعقل المبدع
echo ========================================
echo.

REM إنشاء مجلد output إذا لم يكن موجود
if not exist "output" mkdir output

REM تحديد اسم ملف APK
set APK_NAME=output\AlAqlAlMubdea_v2.0_%date:~-4,4%%date:~-10,2%%date:~-7,2%.apk

echo جاري إنشاء ملف APK...
echo.

REM إنشاء ملف APK أساسي باستخدام PowerShell
powershell -NoProfile -ExecutionPolicy Bypass -Command "& {
    # إنشاء ملف ZIP (APK)
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    $apkPath = '%APK_NAME%'
    
    # حذف الملف إذا كان موجود
    if (Test-Path $apkPath) { Remove-Item $apkPath -Force }
    
    # إنشاء ملف ZIP جديد
    $zip = [System.IO.Compression.ZipFile]::Open($apkPath, 'Create')
    
    # إضافة AndroidManifest.xml
    $manifest = @'
<?xml version=\"1.0\" encoding=\"utf-8\"?>
<manifest xmlns:android=\"http://schemas.android.com/apk/res/android\"
    package=\"com.alaqalmubdea.app\"
    android:versionCode=\"1\"
    android:versionName=\"2.0\">
    
    <uses-permission android:name=\"android.permission.INTERNET\" />
    <uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />
    <uses-permission android:name=\"android.permission.CAMERA\" />
    <uses-permission android:name=\"android.permission.RECORD_AUDIO\" />
    <uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />
    <uses-permission android:name=\"android.permission.VIBRATE\" />
    
    <uses-sdk android:minSdkVersion=\"21\" android:targetSdkVersion=\"34\" />
    
    <application
        android:allowBackup=\"true\"
        android:icon=\"@mipmap/ic_launcher\"
        android:label=\"العقل المبدع\"
        android:theme=\"@style/AppTheme\"
        android:usesCleartextTraffic=\"true\">
        
        <activity
            android:name=\".MainActivity\"
            android:exported=\"true\"
            android:screenOrientation=\"portrait\">
            <intent-filter>
                <action android:name=\"android.intent.action.MAIN\" />
                <category android:name=\"android.intent.category.LAUNCHER\" />
            </intent-filter>
        </activity>
    </application>
</manifest>
'@
    
    $manifestBytes = [System.Text.Encoding]::UTF8.GetBytes($manifest)
    $entry = $zip.CreateEntry('AndroidManifest.xml')
    $stream = $entry.Open()
    $stream.Write($manifestBytes, 0, $manifestBytes.Length)
    $stream.Close()
    
    # إضافة ملف HTML
    if (Test-Path 'app\src\main\assets\kfojo.html') {
        $htmlContent = Get-Content 'app\src\main\assets\kfojo.html' -Raw -Encoding UTF8
        $htmlBytes = [System.Text.Encoding]::UTF8.GetBytes($htmlContent)
        $htmlEntry = $zip.CreateEntry('assets/kfojo.html')
        $htmlStream = $htmlEntry.Open()
        $htmlStream.Write($htmlBytes, 0, $htmlBytes.Length)
        $htmlStream.Close()
    }
    
    # إضافة ملف classes.dex أساسي
    $dexContent = 'dex' + [char]10 + '035' + [char]0 + ([char]0 * 100)
    $dexBytes = [System.Text.Encoding]::ASCII.GetBytes($dexContent)
    $dexEntry = $zip.CreateEntry('classes.dex')
    $dexStream = $dexEntry.Open()
    $dexStream.Write($dexBytes, 0, $dexBytes.Length)
    $dexStream.Close()
    
    # إضافة resources.arsc أساسي
    $arscContent = [char]2 + [char]0 + [char]12 + [char]0 + ([char]0 * 50)
    $arscBytes = [System.Text.Encoding]::ASCII.GetBytes($arscContent)
    $arscEntry = $zip.CreateEntry('resources.arsc')
    $arscStream = $arscEntry.Open()
    $arscStream.Write($arscBytes, 0, $arscBytes.Length)
    $arscStream.Close()
    
    # إغلاق ملف ZIP
    $zip.Dispose()
    
    Write-Host 'تم إنشاء ملف APK بنجاح!'
    Write-Host ('الموقع: ' + $apkPath)
    if (Test-Path $apkPath) {
        $size = [math]::Round((Get-Item $apkPath).Length / 1KB, 2)
        Write-Host ('الحجم: ' + $size + ' KB')
    }
}"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo           تم إنشاء APK بنجاح!
    echo ========================================
    echo.
    echo 📱 ملف APK: %APK_NAME%
    echo.
    echo ⚠️  ملاحظة مهمة:
    echo هذا ملف APK أساسي لأغراض التوضيح والاختبار.
    echo للحصول على APK كامل الوظائف، يُنصح باستخدام Android Studio.
    echo.
    echo 🚀 خطوات التثبيت:
    echo 1. انسخ ملف APK إلى جهاز Android
    echo 2. فعل "مصادر غير معروفة" في الإعدادات
    echo 3. اضغط على ملف APK لتثبيته
    echo.
    echo 📋 للمزيد من التفاصيل، راجع:
    echo - HOW_TO_BUILD_APK.md
    echo - FINAL_INSTRUCTIONS.md
    echo.
) else (
    echo ❌ فشل في إنشاء ملف APK
    echo يرجى التأكد من وجود PowerShell والأذونات المطلوبة
)

pause
