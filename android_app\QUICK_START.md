# دليل البدء السريع - تطبيق العقل المبدع Android

## 🚀 البدء السريع

### 1. متطلبات النظام
- ✅ Android Studio Arctic Fox أو أحدث
- ✅ Java 8+ 
- ✅ Android SDK (API 21+)
- ✅ مساحة فارغة 2GB+

### 2. إعداد المشروع

```bash
# 1. تحديث مسار SDK
# قم بتحرير ملف local.properties
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk

# 2. بناء التطبيق (Windows)
build_apk.bat

# أو (Linux/Mac)
chmod +x build_apk.sh
./build_apk.sh
```

### 3. تثبيت التطبيق

```bash
# تثبيت على الجهاز المتصل
adb install output/AlAqlAlMubdea_v2.0_debug.apk

# أو استخدام Android Studio
# File > Open > اختر مجلد android_app
# Run > Run 'app'
```

## 📱 ميزات التطبيق

### ✨ الميزات الأساسية
- 🌐 WebView محسن للأداء العالي
- 🇸🇦 دعم كامل للغة العربية (RTL)
- 📱 تصميم متجاوب للجوال
- 🎨 واجهة جميلة مع تأثيرات بصرية
- 🔄 شاشة تحميل مخصصة
- 📊 6 أقسام رئيسية (وصفات، مشاريع، تعليم، صحة، قصص، معاملات)

### 🔐 الأذونات
- 🌐 الإنترنت والشبكة
- 📷 الكاميرا والميكروفون  
- 📁 قراءة وكتابة الملفات
- 📍 الموقع الجغرافي
- 📳 الاهتزاز

### 🛠️ واجهة JavaScript
```javascript
// أمثلة على الاستخدام
Android.showToast("مرحباً!");
Android.vibrate(100);
Android.saveData("key", "value");
Android.shareText("نص للمشاركة");
```

## 🏗️ بنية المشروع

```
android_app/
├── 📁 app/src/main/
│   ├── 📄 AndroidManifest.xml     # إعدادات التطبيق
│   ├── 📁 java/                   # كود Java
│   ├── 📁 res/                    # الموارد والتصميم
│   └── 📁 assets/                 # ملفات HTML
├── 📄 build.gradle               # إعدادات البناء
├── 📄 build_apk.bat             # سكريبت البناء (Windows)
├── 📄 build_apk.sh              # سكريبت البناء (Linux/Mac)
└── 📄 README.md                 # دليل مفصل
```

## ⚡ أوامر سريعة

```bash
# تنظيف المشروع
./gradlew clean

# بناء إصدار التطوير
./gradlew assembleDebug

# بناء إصدار الإنتاج  
./gradlew assembleRelease

# تثبيت على الجهاز
./gradlew installDebug

# عرض الأجهزة المتصلة
adb devices

# عرض سجلات التطبيق
adb logcat | grep "AlAqlAlMubdea"
```

## 🎨 التخصيص

### تغيير الألوان
```xml
<!-- app/src/main/res/values/colors.xml -->
<color name="purple_500">#c039ff</color>
<color name="purple_700">#6a11cb</color>
```

### تغيير النصوص
```xml
<!-- app/src/main/res/values/strings.xml -->
<string name="app_name">العقل المبدع</string>
```

### تغيير الأيقونة
- استبدل الملفات في `app/src/main/res/mipmap-*/`
- استخدم أدوات مثل Android Asset Studio

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### ❌ خطأ SDK Path
```bash
# الحل: تحديث local.properties
sdk.dir=/path/to/android/sdk
```

#### ❌ خطأ Java
```bash
# التحقق من إصدار Java
java -version

# يجب أن يكون Java 8 أو أحدث
```

#### ❌ خطأ Gradle
```bash
# تنظيف وإعادة البناء
./gradlew clean
./gradlew build
```

#### ❌ مشاكل الأذونات
- تأكد من منح جميع الأذونات في إعدادات التطبيق
- تحقق من `AndroidManifest.xml`

### سجلات مفيدة
```bash
# سجلات التطبيق
adb logcat | grep "AlAqlAlMubdea"

# سجلات WebView
adb logcat | grep "chromium"

# سجلات النظام
adb logcat | grep "System"
```

## 📦 إنشاء APK للنشر

### 1. إنشاء Keystore
```bash
keytool -genkey -v -keystore release.keystore \
  -alias alaqalmubdea -keyalg RSA -keysize 2048 \
  -validity 10000
```

### 2. تحديث إعدادات التوقيع
```gradle
// app/build.gradle
android {
    signingConfigs {
        release {
            storeFile file('release.keystore')
            storePassword 'your_password'
            keyAlias 'alaqalmubdea'
            keyPassword 'your_password'
        }
    }
}
```

### 3. بناء APK موقع
```bash
./gradlew assembleRelease
```

## 📊 معلومات الأداء

### حجم التطبيق
- 📦 APK Debug: ~15-20 MB
- 📦 APK Release: ~8-12 MB (مع ProGuard)

### متطلبات الجهاز
- 📱 Android 5.0+ (API 21)
- 💾 RAM: 2GB+ مُوصى
- 💿 مساحة: 50MB+

### الأداء
- ⚡ وقت البدء: 2-4 ثواني
- 🔄 وقت التحميل: 1-2 ثانية
- 🔋 استهلاك البطارية: منخفض

## 🆘 الدعم

### الحصول على المساعدة
- 📖 راجع `README.md` للدليل المفصل
- 🐛 أبلغ عن المشاكل في Issues
- 💬 تواصل مع فريق التطوير

### الموارد المفيدة
- [Android Developer Guide](https://developer.android.com/)
- [WebView Documentation](https://developer.android.com/guide/webapps/webview)
- [Gradle Build Tool](https://gradle.org/)

---

## ✅ قائمة التحقق السريع

- [ ] تثبيت Android Studio
- [ ] تحديث local.properties
- [ ] تشغيل build_apk.bat/sh
- [ ] التحقق من ملفات APK في مجلد output/
- [ ] تثبيت التطبيق على الجهاز
- [ ] اختبار جميع الميزات

---

**🎉 مبروك! تطبيق العقل المبدع جاهز للاستخدام**

تم تطوير هذا التطبيق بواسطة فريق العقل المبدع 💜
