<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="@color/button_primary_pressed"
                android:endColor="@color/gradient_end"
                android:type="linear" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/border_accent" />
        </shape>
    </item>
    
    <!-- حالة التركيز -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="@color/button_primary"
                android:endColor="@color/gradient_middle"
                android:type="linear" />
            <corners android:radius="12dp" />
            <stroke
                android:width="2dp"
                android:color="@color/focus_color" />
        </shape>
    </item>
    
    <!-- حالة التعطيل -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/button_disabled" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/border_medium" />
        </shape>
    </item>
    
    <!-- الحالة العادية -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="@color/button_primary"
                android:endColor="@color/gradient_middle"
                android:type="linear" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/border_accent" />
        </shape>
    </item>
    
</selector>
