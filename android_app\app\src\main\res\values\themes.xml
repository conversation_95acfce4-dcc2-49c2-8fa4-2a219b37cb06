<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    
    <!-- Theme الأساسي للتطبيق -->
    <style name="Theme.AlAqlAlMubdea" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- الألوان الأساسية -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- الألوان الثانوية -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <!-- ألوان الخلفية -->
        <item name="android:colorBackground">@color/background_dark</item>
        <item name="colorSurface">@color/background_light</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_secondary</item>
        
        <!-- ألوان الحالة -->
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/white</item>
        
        <!-- إعدادات شريط الحالة -->
        <item name="android:statusBarColor">@color/background_darker</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">false</item>
        
        <!-- إعدادات شريط التنقل -->
        <item name="android:navigationBarColor">@color/background_darker</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="o">false</item>
        
        <!-- إعدادات النوافذ -->
        <item name="android:windowBackground">@color/background_dark</item>
        <item name="android:windowAnimationStyle">@style/WindowAnimationTransition</item>
        
        <!-- إعدادات النصوص -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorTertiary">@color/text_tertiary</item>
        
        <!-- إعدادات الأزرار -->
        <item name="materialButtonStyle">@style/Widget.AlAqlAlMubdea.Button</item>
        <item name="borderlessButtonStyle">@style/Widget.AlAqlAlMubdea.Button.Borderless</item>
        
        <!-- إعدادات حقول الإدخال -->
        <item name="textInputStyle">@style/Widget.AlAqlAlMubdea.TextInputLayout</item>
        
        <!-- إعدادات البطاقات -->
        <item name="materialCardViewStyle">@style/Widget.AlAqlAlMubdea.CardView</item>
        
        <!-- إعدادات شريط التطبيق -->
        <item name="toolbarStyle">@style/Widget.AlAqlAlMubdea.Toolbar</item>
        
        <!-- إعدادات التمرير -->
        <item name="android:scrollbarThumbVertical">@color/purple_500</item>
        <item name="android:scrollbarTrackVertical">@color/background_light</item>
        
        <!-- إعدادات التحديد -->
        <item name="android:textColorHighlight">@color/selection_background</item>
        <item name="android:textSelectHandleLeft">@drawable/text_select_handle_left</item>
        <item name="android:textSelectHandleRight">@drawable/text_select_handle_right</item>
        <item name="android:textSelectHandle">@drawable/text_select_handle_middle</item>
        
        <!-- إعدادات الانتقالات -->
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowAllowEnterTransitionOverlap">true</item>
        <item name="android:windowAllowReturnTransitionOverlap">true</item>
        <item name="android:windowSharedElementEnterTransition">@transition/change_image_transform</item>
        <item name="android:windowSharedElementExitTransition">@transition/change_image_transform</item>
    </style>

    <!-- Theme بدون شريط الإجراءات -->
    <style name="Theme.AlAqlAlMubdea.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>

    <!-- Theme شاشة البداية -->
    <style name="Theme.AlAqlAlMubdea.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/background_dark</item>
        <item name="windowSplashScreenAnimatedIcon">@mipmap/ic_launcher</item>
        <item name="windowSplashScreenAnimationDuration">1000</item>
        <item name="postSplashScreenTheme">@style/Theme.AlAqlAlMubdea.NoActionBar</item>
    </style>

    <!-- أنماط الأزرار -->
    <style name="Widget.AlAqlAlMubdea.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/button_primary</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.02</item>
        <item name="elevation">4dp</item>
        <item name="android:stateListAnimator">@animator/button_state_list_animator</item>
    </style>

    <style name="Widget.AlAqlAlMubdea.Button.Borderless" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/purple_500</item>
        <item name="rippleColor">@color/ripple_color</item>
    </style>

    <style name="Widget.AlAqlAlMubdea.Button.Outlined" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textColor">@color/purple_500</item>
        <item name="strokeColor">@color/purple_500</item>
        <item name="strokeWidth">2dp</item>
        <item name="cornerRadius">12dp</item>
    </style>

    <!-- أنماط حقول الإدخال -->
    <style name="Widget.AlAqlAlMubdea.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/input_border_focused</item>
        <item name="boxStrokeWidth">2dp</item>
        <item name="boxCornerRadiusTopStart">12dp</item>
        <item name="boxCornerRadiusTopEnd">12dp</item>
        <item name="boxCornerRadiusBottomStart">12dp</item>
        <item name="boxCornerRadiusBottomEnd">12dp</item>
        <item name="hintTextColor">@color/input_hint</item>
        <item name="android:textColorHint">@color/input_hint</item>
    </style>

    <!-- أنماط البطاقات -->
    <style name="Widget.AlAqlAlMubdea.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">8dp</item>
        <item name="strokeColor">@color/card_border</item>
        <item name="strokeWidth">1dp</item>
    </style>

    <!-- أنماط شريط الأدوات -->
    <style name="Widget.AlAqlAlMubdea.Toolbar" parent="Widget.MaterialComponents.Toolbar.Primary">
        <item name="android:background">@color/background_dark</item>
        <item name="titleTextColor">@color/text_primary</item>
        <item name="subtitleTextColor">@color/text_secondary</item>
        <item name="android:elevation">4dp</item>
    </style>

    <!-- أنماط النصوص -->
    <style name="TextAppearance.AlAqlAlMubdea.Headline1" parent="TextAppearance.MaterialComponents.Headline1">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
        <item name="fontFamily">@font/cairo_bold</item>
    </style>

    <style name="TextAppearance.AlAqlAlMubdea.Headline2" parent="TextAppearance.MaterialComponents.Headline2">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="fontFamily">@font/cairo_bold</item>
    </style>

    <style name="TextAppearance.AlAqlAlMubdea.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">16sp</item>
        <item name="fontFamily">@font/cairo_regular</item>
        <item name="android:lineSpacingExtra">4dp</item>
    </style>

    <style name="TextAppearance.AlAqlAlMubdea.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="android:textColor">@color/text_tertiary</item>
        <item name="android:textSize">14sp</item>
        <item name="fontFamily">@font/cairo_regular</item>
    </style>

    <!-- أنماط التحميل -->
    <style name="Widget.AlAqlAlMubdea.ProgressBar" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:progressTint">@color/purple_500</item>
        <item name="android:progressBackgroundTint">@color/progress_background</item>
        <item name="android:indeterminateTint">@color/purple_500</item>
    </style>

    <!-- أنماط التبديل -->
    <style name="Widget.AlAqlAlMubdea.Switch" parent="Widget.MaterialComponents.CompoundButton.Switch">
        <item name="thumbTint">@color/purple_500</item>
        <item name="trackTint">@color/progress_background</item>
    </style>

    <!-- أنماط الحوارات -->
    <style name="ThemeOverlay.AlAqlAlMubdea.Dialog" parent="ThemeOverlay.MaterialComponents.Dialog">
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorSurface">@color/background_light</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="android:windowBackground">@drawable/dialog_background</item>
    </style>

    <!-- أنماط القوائم المنسدلة -->
    <style name="Widget.AlAqlAlMubdea.PopupMenu" parent="Widget.MaterialComponents.PopupMenu">
        <item name="android:popupBackground">@drawable/popup_background</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <!-- أنماط التبويبات -->
    <style name="Widget.AlAqlAlMubdea.TabLayout" parent="Widget.MaterialComponents.TabLayout">
        <item name="tabIndicatorColor">@color/purple_500</item>
        <item name="tabSelectedTextColor">@color/purple_500</item>
        <item name="tabTextColor">@color/text_tertiary</item>
        <item name="android:background">@color/background_light</item>
    </style>

    <!-- أنماط الشرائح -->
    <style name="Widget.AlAqlAlMubdea.Slider" parent="Widget.MaterialComponents.Slider">
        <item name="thumbColor">@color/purple_500</item>
        <item name="trackColorActive">@color/purple_500</item>
        <item name="trackColorInactive">@color/progress_background</item>
    </style>

</resources>
