# العقل المبدع - تطبيق Android WebView

تطبيق Android أصلي يعرض محتوى "العقل المبدع" باستخدام WebView مع جميع الميزات والأذونات المطلوبة.

## الميزات

- ✅ WebView محسن للأداء العالي
- ✅ دعم كامل للغة العربية (RTL)
- ✅ أذونات شاملة (الكاميرا، الميكروفون، التخزين، الموقع)
- ✅ واجهة JavaScript للتفاعل مع النظام الأصلي
- ✅ دعم رفع الملفات والصور
- ✅ شاشة تحميل مخصصة
- ✅ أيقونة تطبيق جميلة
- ✅ تحسينات خاصة بالجوال
- ✅ دعم السحب للتحديث
- ✅ معالجة حالات عدم الاتصال بالإنترنت

## متطلبات النظام

- Android Studio Arctic Fox أو أحدث
- Android SDK 21+ (Android 5.0+)
- Java 8 أو أحدث
- Gradle 8.4

## التثبيت والبناء

### 1. إعداد البيئة

```bash
# تأكد من تثبيت Android Studio و SDK
# قم بتحديث مسار SDK في local.properties
```

### 2. استنساخ المشروع

```bash
git clone <repository-url>
cd android_app
```

### 3. تحديث إعدادات SDK

قم بتحديث ملف `local.properties` بمسار Android SDK الصحيح:

```properties
sdk.dir=C\:\\Users\\YourUsername\\AppData\\Local\\Android\\Sdk
```

### 4. بناء التطبيق

#### بناء إصدار التطوير (Debug)

```bash
./gradlew assembleDebug
```

#### بناء إصدار الإنتاج (Release)

```bash
./gradlew assembleRelease
```

### 5. تثبيت التطبيق

```bash
# تثبيت إصدار التطوير
./gradlew installDebug

# أو تثبيت إصدار الإنتاج
./gradlew installRelease
```

## بنية المشروع

```
android_app/
├── app/
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/alaqalmubdea/app/
│   │   │   │   ├── MainActivity.java          # النشاط الرئيسي
│   │   │   │   └── WebAppInterface.java       # واجهة JavaScript
│   │   │   ├── res/
│   │   │   │   ├── layout/                    # تخطيطات الواجهة
│   │   │   │   ├── values/                    # القيم والألوان والنصوص
│   │   │   │   ├── drawable/                  # الرسوم والأيقونات
│   │   │   │   ├── mipmap-*/                  # أيقونات التطبيق
│   │   │   │   └── xml/                       # ملفات XML المساعدة
│   │   │   ├── assets/
│   │   │   │   └── kfojo.html                 # ملف HTML الرئيسي
│   │   │   └── AndroidManifest.xml            # بيان التطبيق
│   │   └── build.gradle                       # إعدادات بناء التطبيق
│   └── proguard-rules.pro                     # قواعد ProGuard
├── gradle/
│   └── wrapper/                               # Gradle Wrapper
├── build.gradle                               # إعدادات المشروع الرئيسية
├── settings.gradle                            # إعدادات Gradle
├── gradle.properties                          # خصائص Gradle
└── local.properties                           # إعدادات محلية
```

## الأذونات المطلوبة

التطبيق يطلب الأذونات التالية:

- `INTERNET` - للاتصال بالإنترنت
- `ACCESS_NETWORK_STATE` - لفحص حالة الشبكة
- `ACCESS_WIFI_STATE` - لفحص حالة WiFi
- `CAMERA` - لاستخدام الكاميرا
- `RECORD_AUDIO` - لتسجيل الصوت
- `READ_EXTERNAL_STORAGE` - لقراءة الملفات
- `WRITE_EXTERNAL_STORAGE` - لكتابة الملفات
- `READ_MEDIA_*` - لقراءة الوسائط (Android 13+)
- `VIBRATE` - للاهتزاز
- `WAKE_LOCK` - لمنع النوم

## واجهة JavaScript

التطبيق يوفر واجهة JavaScript للتفاعل مع النظام الأصلي:

```javascript
// عرض رسالة
Android.showToast("مرحباً!");

// اهتزاز الجهاز
Android.vibrate(100);

// حفظ البيانات
Android.saveData("key", "value");

// استرجاع البيانات
var data = Android.getData("key");

// مشاركة النص
Android.shareText("نص للمشاركة");

// فتح رابط خارجي
Android.openExternalLink("https://example.com");

// التحقق من الاتصال
var isOnline = Android.isNetworkAvailable();
```

## التخصيص

### تغيير الألوان

قم بتعديل ملف `app/src/main/res/values/colors.xml`:

```xml
<color name="purple_500">#c039ff</color>
<color name="purple_700">#6a11cb</color>
```

### تغيير النصوص

قم بتعديل ملف `app/src/main/res/values/strings.xml`:

```xml
<string name="app_name">العقل المبدع</string>
```

### تغيير الأيقونة

استبدل الملفات في مجلدات `mipmap-*` بأيقونات جديدة.

## إنشاء APK موقع

### 1. إنشاء Keystore

```bash
keytool -genkey -v -keystore release.keystore -alias alaqalmubdea -keyalg RSA -keysize 2048 -validity 10000
```

### 2. تحديث إعدادات التوقيع

أضف إلى `app/build.gradle`:

```gradle
android {
    signingConfigs {
        release {
            storeFile file('path/to/release.keystore')
            storePassword 'your_store_password'
            keyAlias 'alaqalmubdea'
            keyPassword 'your_key_password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }
}
```

### 3. بناء APK موقع

```bash
./gradlew assembleRelease
```

## استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في SDK Path**
   - تأكد من تحديث `local.properties` بالمسار الصحيح

2. **خطأ في الأذونات**
   - تأكد من منح جميع الأذونات المطلوبة

3. **مشاكل WebView**
   - تأكد من تفعيل JavaScript في WebView
   - تحقق من إعدادات الأمان

4. **مشاكل البناء**
   - نظف المشروع: `./gradlew clean`
   - أعد بناء المشروع: `./gradlew build`

### سجلات التطبيق

```bash
# عرض سجلات التطبيق
adb logcat | grep "AlAqlAlMubdea"

# عرض سجلات WebView
adb logcat | grep "chromium"
```

## الدعم والمساهمة

- للإبلاغ عن مشاكل: أنشئ Issue جديد
- للمساهمة: أرسل Pull Request
- للدعم: تواصل مع فريق التطوير

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## معلومات الإصدار

- **الإصدار الحالي**: 2.0
- **الحد الأدنى لـ Android**: 5.0 (API 21)
- **الهدف**: Android 14 (API 34)
- **تاريخ آخر تحديث**: 2024

---

تم تطوير هذا التطبيق بواسطة فريق العقل المبدع 💜
