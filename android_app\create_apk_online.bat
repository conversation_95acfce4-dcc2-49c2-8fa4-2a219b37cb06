@echo off
chcp 65001 >nul
echo ========================================
echo    إنشاء APK باستخدام الأدوات السحابية
echo ========================================
echo.

echo هذا السكريبت سيساعدك في إنشاء APK حقيقي باستخدام أدوات سحابية مجانية
echo.

echo 🌐 الطرق المتاحة لإنشاء APK:
echo.

echo ==========================================
echo 1. PhoneGap Build (Adobe) - مجاني
echo ==========================================
echo الموقع: https://build.phonegap.com
echo الخطوات:
echo 1. إنشاء حساب مجاني
echo 2. رفع ملف ZIP يحتوي على:
echo    - index.html (ملف kfojo.html)
echo    - config.xml
echo 3. بناء APK تلقائياً
echo.

echo ==========================================
echo 2. Monaca (مجاني مع قيود)
echo ==========================================
echo الموقع: https://monaca.io
echo الخطوات:
echo 1. إنشاء حساب مجاني
echo 2. إنشاء مشروع Cordova جديد
echo 3. رفع ملف HTML
echo 4. بناء APK
echo.

echo ==========================================
echo 3. AppGyver (مجاني)
echo ==========================================
echo الموقع: https://www.appgyver.com
echo الخطوات:
echo 1. إنشاء حساب مجاني
echo 2. إنشاء تطبيق WebView
echo 3. إدخال رابط التطبيق
echo 4. بناء APK
echo.

echo ==========================================
echo 4. Ionic Appflow (مجاني مع قيود)
echo ==========================================
echo الموقع: https://ionicframework.com/appflow
echo الخطوات:
echo 1. إنشاء حساب مجاني
echo 2. إنشاء مشروع Ionic
echo 3. رفع الكود
echo 4. بناء APK
echo.

echo ==========================================
echo 5. GitHub Actions (مجاني)
echo ==========================================
echo إذا كان لديك حساب GitHub:
echo 1. رفع مشروع android_app إلى GitHub
echo 2. إنشاء GitHub Action للبناء التلقائي
echo 3. تحميل APK من Artifacts
echo.

echo ==========================================
echo 6. Capacitor + Vercel (مجاني)
echo ==========================================
echo الموقع: https://capacitorjs.com
echo الخطوات:
echo 1. تثبيت Capacitor
echo 2. إنشاء مشروع جديد
echo 3. إضافة منصة Android
echo 4. بناء APK
echo.

echo.
echo 🚀 الطريقة الأسرع والأسهل:
echo.

echo ==========================================
echo ApkOnline.com (فوري ومجاني)
echo ==========================================
echo الموقع: https://www.apkonline.net
echo الخطوات:
echo 1. اذهب إلى الموقع
echo 2. اختر "HTML to APK"
echo 3. ارفع ملف kfojo.html
echo 4. أدخل معلومات التطبيق:
echo    - الاسم: العقل المبدع
echo    - الحزمة: com.alaqalmubdea.app
echo    - الإصدار: 2.0
echo 5. اضغط "Generate APK"
echo 6. حمل APK الجاهز
echo.

echo ==========================================
echo Website 2 APK Builder (فوري)
echo ==========================================
echo الموقع: https://website2apk.com
echo الخطوات:
echo 1. اذهب إلى الموقع
echo 2. أدخل رابط التطبيق أو ارفع ملف HTML
echo 3. اختر الإعدادات:
echo    - اسم التطبيق: العقل المبدع
echo    - أيقونة التطبيق
echo    - الألوان
echo 4. اضغط "Create APK"
echo 5. حمل APK
echo.

echo ==========================================
echo تحضير الملفات للرفع
echo ==========================================
echo.

REM إنشاء مجلد للملفات الجاهزة للرفع
if not exist "output\for_upload" mkdir "output\for_upload"

REM نسخ ملف HTML
if exist "app\src\main\assets\kfojo.html" (
    copy "app\src\main\assets\kfojo.html" "output\for_upload\index.html" >nul
    echo ✅ تم نسخ ملف HTML إلى: output\for_upload\index.html
) else (
    echo ❌ لم يتم العثور على ملف HTML الأصلي
)

REM إنشاء ملف config.xml
(
echo ^<?xml version="1.0" encoding="UTF-8" ?^>
echo ^<widget xmlns="http://www.w3.org/ns/widgets"
echo         xmlns:gap="http://phonegap.com/ns/1.0"
echo         id="com.alaqalmubdea.app"
echo         version="2.0.0"^>
echo.
echo     ^<name^>العقل المبدع^</name^>
echo.
echo     ^<description^>
echo         منصة شاملة للإبداع والتعلم
echo     ^</description^>
echo.
echo     ^<author href="https://alaqalmubdea.com" email="<EMAIL>"^>
echo         فريق العقل المبدع
echo     ^</author^>
echo.
echo     ^<content src="index.html" /^>
echo.
echo     ^<preference name="permissions" value="none" /^>
echo     ^<preference name="orientation" value="portrait" /^>
echo     ^<preference name="target-device" value="universal" /^>
echo     ^<preference name="fullscreen" value="false" /^>
echo     ^<preference name="webviewbounce" value="true" /^>
echo     ^<preference name="prerendered-icon" value="true" /^>
echo     ^<preference name="stay-in-webview" value="false" /^>
echo     ^<preference name="ios-statusbarstyle" value="black-opaque" /^>
echo     ^<preference name="detect-data-types" value="true" /^>
echo     ^<preference name="exit-on-suspend" value="false" /^>
echo     ^<preference name="show-splash-screen-spinner" value="true" /^>
echo     ^<preference name="auto-hide-splash-screen" value="true" /^>
echo     ^<preference name="disable-cursor" value="false" /^>
echo     ^<preference name="android-minSdkVersion" value="21" /^>
echo     ^<preference name="android-installLocation" value="auto" /^>
echo.
echo     ^<gap:plugin name="org.apache.cordova.battery-status" /^>
echo     ^<gap:plugin name="org.apache.cordova.camera" /^>
echo     ^<gap:plugin name="org.apache.cordova.media-capture" /^>
echo     ^<gap:plugin name="org.apache.cordova.console" /^>
echo     ^<gap:plugin name="org.apache.cordova.contacts" /^>
echo     ^<gap:plugin name="org.apache.cordova.device" /^>
echo     ^<gap:plugin name="org.apache.cordova.device-motion" /^>
echo     ^<gap:plugin name="org.apache.cordova.device-orientation" /^>
echo     ^<gap:plugin name="org.apache.cordova.dialogs" /^>
echo     ^<gap:plugin name="org.apache.cordova.file" /^>
echo     ^<gap:plugin name="org.apache.cordova.file-transfer" /^>
echo     ^<gap:plugin name="org.apache.cordova.geolocation" /^>
echo     ^<gap:plugin name="org.apache.cordova.globalization" /^>
echo     ^<gap:plugin name="org.apache.cordova.inappbrowser" /^>
echo     ^<gap:plugin name="org.apache.cordova.media" /^>
echo     ^<gap:plugin name="org.apache.cordova.network-information" /^>
echo     ^<gap:plugin name="org.apache.cordova.splashscreen" /^>
echo     ^<gap:plugin name="org.apache.cordova.vibration" /^>
echo.
echo     ^<icon src="icon.png" /^>
echo.
echo     ^<access origin="*" /^>
echo     ^<allow-navigation href="*" /^>
echo     ^<allow-intent href="*" /^>
echo.
echo ^</widget^>
) > "output\for_upload\config.xml"

echo ✅ تم إنشاء ملف config.xml

REM إنشاء ملف ZIP للرفع
echo.
echo 📦 إنشاء ملف ZIP للرفع...
powershell -Command "Compress-Archive -Path 'output\for_upload\*' -DestinationPath 'output\AlAqlAlMubdea_for_upload.zip' -Force"
if exist "output\AlAqlAlMubdea_for_upload.zip" (
    echo ✅ تم إنشاء ملف ZIP: output\AlAqlAlMubdea_for_upload.zip
) else (
    echo ❌ فشل في إنشاء ملف ZIP
)

echo.
echo ========================================
echo           الملفات جاهزة للرفع!
echo ========================================
echo.

echo 📁 الملفات المُنشأة:
echo - output\for_upload\index.html
echo - output\for_upload\config.xml  
echo - output\AlAqlAlMubdea_for_upload.zip
echo.

echo 🚀 الخطوات التالية:
echo 1. اذهب إلى أي من المواقع المذكورة أعلاه
echo 2. ارفع ملف ZIP أو ملف HTML
echo 3. أدخل معلومات التطبيق
echo 4. اضغط "Build" أو "Generate"
echo 5. حمل ملف APK الجاهز
echo.

echo 💡 الأسرع والأسهل: https://www.apkonline.net
echo.

pause
