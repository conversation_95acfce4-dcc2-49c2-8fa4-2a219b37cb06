# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
-keepclassmembers class fqcn.of.javascript.interface.for.webview {
   public *;
}

# Keep WebView JavaScript Interface
-keepclassmembers class com.alaqalmubdea.app.WebAppInterface {
    @android.webkit.JavascriptInterface <methods>;
}

# Keep MainActivity
-keep class com.alaqalmubdea.app.MainActivity { *; }
-keep class com.alaqalmubdea.app.WebAppInterface { *; }

# Keep WebView related classes
-keep class android.webkit.** { *; }
-keep class androidx.webkit.** { *; }

# Keep all classes that might be used by WebView
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Uncomment this to preserve the line number information for
# debugging stack traces.
-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
-renamesourcefileattribute SourceFile

# Keep AndroidX classes
-keep class androidx.** { *; }
-dontwarn androidx.**

# Keep Google Material Design classes
-keep class com.google.android.material.** { *; }
-dontwarn com.google.android.material.**

# Keep support library classes
-keep class android.support.** { *; }
-dontwarn android.support.**

# Keep Gson classes
-keep class com.google.gson.** { *; }
-dontwarn com.google.gson.**

# Keep OkHttp classes
-keep class okhttp3.** { *; }
-keep class okio.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**

# Keep Glide classes
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
 <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-keep class com.bumptech.glide.load.data.ParcelFileDescriptorRewinder$InternalRewinder {
  *** rewind();
}

# Keep Dexter permission library
-keep class com.karumi.dexter.** { *; }
-dontwarn com.karumi.dexter.**

# Keep Room database classes
-keep class androidx.room.** { *; }
-dontwarn androidx.room.**

# Keep security crypto classes
-keep class androidx.security.crypto.** { *; }
-dontwarn androidx.security.crypto.**

# Keep multidex classes
-keep class androidx.multidex.** { *; }
-dontwarn androidx.multidex.**

# Keep vector drawable classes
-keep class androidx.vectordrawable.** { *; }
-dontwarn androidx.vectordrawable.**

# Keep Fresco classes
-keep class com.facebook.fresco.** { *; }
-dontwarn com.facebook.fresco.**

# Keep Apache Commons IO
-keep class org.apache.commons.io.** { *; }
-dontwarn org.apache.commons.io.**

# Keep Bouncy Castle
-keep class org.bouncycastle.** { *; }
-dontwarn org.bouncycastle.**

# Keep ThreeTen ABP
-keep class com.jakewharton.threetenabp.** { *; }
-dontwarn com.jakewharton.threetenabp.**

# Keep application classes
-keep class com.alaqalmubdea.app.** { *; }

# Keep all model classes (if any)
-keep class com.alaqalmubdea.app.model.** { *; }

# Keep all utility classes
-keep class com.alaqalmubdea.app.utils.** { *; }

# Keep all custom views
-keep class com.alaqalmubdea.app.views.** { *; }

# Keep all adapters
-keep class com.alaqalmubdea.app.adapters.** { *; }

# Keep all fragments
-keep class com.alaqalmubdea.app.fragments.** { *; }

# Keep all services
-keep class com.alaqalmubdea.app.services.** { *; }

# Keep all receivers
-keep class com.alaqalmubdea.app.receivers.** { *; }

# Keep all providers
-keep class com.alaqalmubdea.app.providers.** { *; }

# Keep enums
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep Parcelable classes
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# Keep Serializable classes
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep custom attributes
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Optimization settings
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# Keep line numbers for crash reports
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# Don't warn about missing classes
-dontwarn java.lang.invoke.**
-dontwarn java.nio.file.**
-dontwarn org.codehaus.mojo.animal_sniffer.**

# Keep Firebase classes (if using Firebase)
# -keep class com.google.firebase.** { *; }
# -dontwarn com.google.firebase.**

# Keep Google Play Services (if using)
# -keep class com.google.android.gms.** { *; }
# -dontwarn com.google.android.gms.**
