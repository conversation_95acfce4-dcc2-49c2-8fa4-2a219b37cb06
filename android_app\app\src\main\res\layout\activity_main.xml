<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_dark"
    tools:context=".MainActivity">

    <!-- SwipeRefreshLayout للسحب للتحديث -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- WebView الرئيسي -->
        <WebView
            android:id="@+id/webview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/background_dark"
            android:scrollbars="none"
            android:overScrollMode="never"
            android:nestedScrollingEnabled="true" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- شريط التقدم -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:layout_alignParentTop="true"
        android:background="@color/background_dark"
        android:progressTint="@color/purple_500"
        android:progressBackgroundTint="@color/purple_200"
        android:indeterminate="false"
        android:max="100"
        android:visibility="gone" />

    <!-- شاشة التحميل الأولية -->
    <LinearLayout
        android:id="@+id/loadingScreen"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/splash_background"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <!-- أيقونة التطبيق -->
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="24dp"
            android:src="@mipmap/ic_launcher"
            android:contentDescription="@string/app_name" />

        <!-- اسم التطبيق -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="@string/app_name"
            android:textColor="@color/white"
            android:textSize="24sp"
            android:textStyle="bold"
            android:fontFamily="@font/cairo_bold" />

        <!-- نص التحميل -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:text="@string/loading_text"
            android:textColor="@color/purple_200"
            android:textSize="16sp"
            android:fontFamily="@font/cairo_regular" />

        <!-- مؤشر التحميل -->
        <ProgressBar
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:indeterminate="true"
            android:indeterminateTint="@color/purple_500" />

    </LinearLayout>

    <!-- رسالة عدم وجود اتصال بالإنترنت -->
    <LinearLayout
        android:id="@+id/noInternetLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background_dark"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="32dp"
        android:visibility="gone">

        <!-- أيقونة عدم الاتصال -->
        <ImageView
            android:layout_width="96dp"
            android:layout_height="96dp"
            android:layout_marginBottom="24dp"
            android:src="@drawable/ic_no_internet"
            android:tint="@color/purple_300"
            android:contentDescription="@string/no_internet" />

        <!-- عنوان رسالة عدم الاتصال -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="@string/no_internet_title"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textAlignment="center"
            android:fontFamily="@font/cairo_bold" />

        <!-- وصف رسالة عدم الاتصال -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:text="@string/no_internet_message"
            android:textColor="@color/purple_200"
            android:textSize="16sp"
            android:textAlignment="center"
            android:lineSpacingExtra="4dp"
            android:fontFamily="@font/cairo_regular" />

        <!-- زر إعادة المحاولة -->
        <Button
            android:id="@+id/retryButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/button_background"
            android:text="@string/retry"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:paddingHorizontal="32dp"
            android:paddingVertical="12dp"
            android:fontFamily="@font/cairo_bold"
            android:elevation="4dp" />

    </LinearLayout>

</RelativeLayout>
