package com.alaqalmubdea.app;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Vibrator;
import android.webkit.JavascriptInterface;
import android.widget.Toast;

/**
 * واجهة JavaScript للتفاعل مع التطبيق الأصلي
 */
public class WebAppInterface {
    Context context;
    SharedPreferences sharedPreferences;

    public WebAppInterface(Context context) {
        this.context = context;
        this.sharedPreferences = context.getSharedPreferences("AlAqlAlMubdea", Context.MODE_PRIVATE);
    }

    /**
     * عرض رسالة Toast
     */
    @JavascriptInterface
    public void showToast(String message) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
    }

    /**
     * اهتزاز الجهاز
     */
    @JavascriptInterface
    public void vibrate(int duration) {
        try {
            Vibrator vibrator = (Vibrator) context.getSystemService(Context.VIBRATOR_SERVICE);
            if (vibrator != null && vibrator.hasVibrator()) {
                vibrator.vibrate(duration);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * حفظ البيانات محلياً
     */
    @JavascriptInterface
    public void saveData(String key, String value) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString(key, value);
        editor.apply();
    }

    /**
     * استرجاع البيانات المحفوظة
     */
    @JavascriptInterface
    public String getData(String key) {
        return sharedPreferences.getString(key, "");
    }

    /**
     * حذف البيانات المحفوظة
     */
    @JavascriptInterface
    public void removeData(String key) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.remove(key);
        editor.apply();
    }

    /**
     * مسح جميع البيانات المحفوظة
     */
    @JavascriptInterface
    public void clearAllData() {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.clear();
        editor.apply();
    }

    /**
     * مشاركة النص
     */
    @JavascriptInterface
    public void shareText(String text) {
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, text);
        shareIntent.putExtra(Intent.EXTRA_SUBJECT, "العقل المبدع");
        
        Intent chooserIntent = Intent.createChooser(shareIntent, "مشاركة عبر");
        chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(chooserIntent);
    }

    /**
     * فتح رابط خارجي
     */
    @JavascriptInterface
    public void openExternalLink(String url) {
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        } catch (Exception e) {
            showToast("لا يمكن فتح الرابط");
        }
    }

    /**
     * الحصول على معلومات التطبيق
     */
    @JavascriptInterface
    public String getAppInfo() {
        try {
            String versionName = context.getPackageManager()
                .getPackageInfo(context.getPackageName(), 0).versionName;
            return "العقل المبدع v" + versionName;
        } catch (Exception e) {
            return "العقل المبدع";
        }
    }

    /**
     * التحقق من حالة الاتصال بالإنترنت
     */
    @JavascriptInterface
    public boolean isNetworkAvailable() {
        try {
            android.net.ConnectivityManager connectivityManager = 
                (android.net.ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            android.net.NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * إنهاء التطبيق
     */
    @JavascriptInterface
    public void exitApp() {
        if (context instanceof MainActivity) {
            ((MainActivity) context).finish();
        }
    }

    /**
     * إعادة تحميل التطبيق
     */
    @JavascriptInterface
    public void reloadApp() {
        if (context instanceof MainActivity) {
            ((MainActivity) context).runOnUiThread(() -> {
                ((MainActivity) context).recreate();
            });
        }
    }

    /**
     * تسجيل حدث للتحليلات
     */
    @JavascriptInterface
    public void logEvent(String eventName, String eventData) {
        // يمكن إضافة تحليلات Firebase أو أي خدمة تحليلات أخرى هنا
        android.util.Log.d("AlAqlAlMubdea", "Event: " + eventName + ", Data: " + eventData);
    }

    /**
     * الحصول على معرف الجهاز الفريد
     */
    @JavascriptInterface
    public String getDeviceId() {
        return android.provider.Settings.Secure.getString(
            context.getContentResolver(), 
            android.provider.Settings.Secure.ANDROID_ID
        );
    }

    /**
     * حفظ الإعدادات
     */
    @JavascriptInterface
    public void saveSetting(String key, String value) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString("setting_" + key, value);
        editor.apply();
    }

    /**
     * استرجاع الإعدادات
     */
    @JavascriptInterface
    public String getSetting(String key) {
        return sharedPreferences.getString("setting_" + key, "");
    }

    /**
     * التحقق من وجود إعداد معين
     */
    @JavascriptInterface
    public boolean hasSetting(String key) {
        return sharedPreferences.contains("setting_" + key);
    }
}
