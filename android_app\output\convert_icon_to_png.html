<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحويل أيقونة التطبيق إلى PNG</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        h1 {
            color: #a78bfa;
            margin-bottom: 30px;
            font-size: 2em;
        }
        
        .icon-preview {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 15px;
            border: 2px dashed #a78bfa;
        }
        
        #iconSvg {
            width: 128px;
            height: 128px;
            margin: 10px;
        }
        
        .size-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .size-btn {
            background: #a78bfa;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .size-btn:hover {
            background: #8b5cf6;
            transform: translateY(-2px);
        }
        
        .download-area {
            margin-top: 20px;
            padding: 20px;
            background: #f0f9ff;
            border-radius: 15px;
        }
        
        #downloadLink {
            display: inline-block;
            background: #10b981;
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        #downloadLink:hover {
            background: #059669;
            transform: translateY(-2px);
        }
        
        .info {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            color: #92400e;
        }
        
        canvas {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 تحويل أيقونة العقل المبدع إلى PNG</h1>
        
        <div class="icon-preview">
            <h3>معاينة الأيقونة:</h3>
            <svg id="iconSvg" xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'>
                <path fill='#a78bfa' d='M50,15.2C25.4,15.2,16.5,35.7,18,50.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1C83.5,35.7,74.6,15.2,50,15.2z'/>
                <circle cx='42' cy='55' r='5' fill='white'/>
                <circle cx='58' cy='55' r='5' fill='white'/>
            </svg>
        </div>
        
        <div class="info">
            <strong>💡 تعليمات:</strong><br>
            اختر الحجم المطلوب واضغط على الزر لتحويل الأيقونة إلى PNG بذلك الحجم
        </div>
        
        <div class="size-buttons">
            <button class="size-btn" onclick="convertToPNG(48)">48x48 (صغير)</button>
            <button class="size-btn" onclick="convertToPNG(72)">72x72 (متوسط)</button>
            <button class="size-btn" onclick="convertToPNG(96)">96x96 (كبير)</button>
            <button class="size-btn" onclick="convertToPNG(128)">128x128 (كبير جداً)</button>
            <button class="size-btn" onclick="convertToPNG(192)">192x192 (عالي الدقة)</button>
            <button class="size-btn" onclick="convertToPNG(512)">512x512 (فائق الدقة)</button>
        </div>
        
        <div class="download-area">
            <canvas id="canvas" style="display: none;"></canvas>
            <div id="downloadSection" style="display: none;">
                <h3>✅ تم التحويل بنجاح!</h3>
                <a id="downloadLink" href="#" download="alaqalmubdea_icon.png">
                    📥 تحميل الأيقونة PNG
                </a>
                <p>حجم الأيقونة: <span id="sizeInfo"></span></p>
            </div>
        </div>
        
        <div class="info">
            <strong>📱 للاستخدام في التطبيقات:</strong><br>
            • 48x48 - للأيقونات الصغيرة<br>
            • 96x96 - للأيقونات العادية<br>
            • 192x192 - للأيقونات عالية الدقة<br>
            • 512x512 - لمتاجر التطبيقات
        </div>
    </div>

    <script>
        function convertToPNG(size) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            const downloadSection = document.getElementById('downloadSection');
            const downloadLink = document.getElementById('downloadLink');
            const sizeInfo = document.getElementById('sizeInfo');
            
            // تحديد حجم الكانفاس
            canvas.width = size;
            canvas.height = size;
            
            // إنشاء صورة من SVG
            const svgData = `
                <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' width='${size}' height='${size}'>
                    <path fill='#a78bfa' d='M50,15.2C25.4,15.2,16.5,35.7,18,50.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1C83.5,35.7,74.6,15.2,50,15.2z'/>
                    <circle cx='42' cy='55' r='5' fill='white'/>
                    <circle cx='58' cy='55' r='5' fill='white'/>
                </svg>
            `;
            
            const img = new Image();
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                // مسح الكانفاس
                ctx.clearRect(0, 0, size, size);
                
                // رسم الصورة على الكانفاس
                ctx.drawImage(img, 0, 0, size, size);
                
                // تحويل إلى PNG
                const pngDataUrl = canvas.toDataURL('image/png');
                
                // إعداد رابط التحميل
                downloadLink.href = pngDataUrl;
                downloadLink.download = `alaqalmubdea_icon_${size}x${size}.png`;
                
                // عرض معلومات الحجم
                sizeInfo.textContent = `${size}x${size} بكسل`;
                
                // إظهار قسم التحميل
                downloadSection.style.display = 'block';
                
                // تنظيف الذاكرة
                URL.revokeObjectURL(url);
            };
            
            img.src = url;
        }
        
        // تحويل تلقائي لحجم 128x128 عند تحميل الصفحة
        window.onload = function() {
            setTimeout(() => {
                convertToPNG(128);
            }, 500);
        };
    </script>
</body>
</html>
