<!DOCTYPE html>
<!--
العقل المبدع v2.0 - تطبيق Android WebView
التحسينات المضافة:
- تحسين كامل للعرض على الهواتف المحمولة
- تحسين الأداء والسرعة
- تحسين التفاعل باللمس
- تحسين الخطوط والنصوص العربية
- تحسين المؤثرات البصرية للجوال
- تقليل استهلاك البطارية
- دعم واجهة Android الأصلية
-->
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>العقل المبدع v2.0 - تطبيق Android</title>
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Vazirmatn:wght@400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        input, textarea, button {
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        body {
            font-family: 'Vazirmatn', 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* خلفية متحركة محسنة للجوال */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background:
                radial-gradient(circle at 20% 30%, rgba(147, 197, 253, 0.15) 0%, transparent 70%),
                radial-gradient(circle at 80% 70%, rgba(196, 181, 253, 0.12) 0%, transparent 70%),
                radial-gradient(circle at 50% 50%, rgba(252, 211, 77, 0.08) 0%, transparent 80%),
                radial-gradient(circle at 30% 80%, rgba(167, 243, 208, 0.1) 0%, transparent 75%),
                radial-gradient(circle at 70% 20%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            z-index: -1;
            pointer-events: none;
            animation: backgroundShift 20s ease-in-out infinite;
            will-change: background;
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
        }

        @keyframes backgroundShift {
            0%, 100% {
                background:
                    radial-gradient(circle at 20% 30%, rgba(147, 197, 253, 0.15) 0%, transparent 70%),
                    radial-gradient(circle at 80% 70%, rgba(196, 181, 253, 0.12) 0%, transparent 70%),
                    radial-gradient(circle at 50% 50%, rgba(252, 211, 77, 0.08) 0%, transparent 80%),
                    radial-gradient(circle at 30% 80%, rgba(167, 243, 208, 0.1) 0%, transparent 75%),
                    radial-gradient(circle at 70% 20%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            }
            25% {
                background:
                    radial-gradient(circle at 80% 20%, rgba(196, 181, 253, 0.12) 0%, transparent 70%),
                    radial-gradient(circle at 20% 80%, rgba(167, 243, 208, 0.15) 0%, transparent 70%),
                    radial-gradient(circle at 60% 40%, rgba(147, 197, 253, 0.1) 0%, transparent 75%),
                    radial-gradient(circle at 30% 70%, rgba(252, 211, 77, 0.08) 0%, transparent 80%),
                    radial-gradient(circle at 70% 30%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            }
            50% {
                background:
                    radial-gradient(circle at 60% 80%, rgba(252, 211, 77, 0.12) 0%, transparent 70%),
                    radial-gradient(circle at 40% 20%, rgba(147, 197, 253, 0.15) 0%, transparent 70%),
                    radial-gradient(circle at 80% 60%, rgba(167, 243, 208, 0.1) 0%, transparent 75%),
                    radial-gradient(circle at 20% 40%, rgba(196, 181, 253, 0.08) 0%, transparent 80%),
                    radial-gradient(circle at 50% 80%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            }
            75% {
                background:
                    radial-gradient(circle at 30% 50%, rgba(167, 243, 208, 0.12) 0%, transparent 70%),
                    radial-gradient(circle at 70% 50%, rgba(252, 211, 77, 0.15) 0%, transparent 70%),
                    radial-gradient(circle at 50% 20%, rgba(196, 181, 253, 0.1) 0%, transparent 75%),
                    radial-gradient(circle at 80% 80%, rgba(147, 197, 253, 0.08) 0%, transparent 80%),
                    radial-gradient(circle at 20% 20%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            }
        }

        :root { 
            --main-color: #c039ff; 
            --secondary-color: #6a11cb; 
        }

        .glassmorphism {
            background: rgba(255, 255, 255, 0.02);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.03);
            border-radius: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .main-button {
            background: linear-gradient(135deg, var(--main-color), var(--secondary-color));
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(192, 57, 255, 0.3);
            border: none;
            font-weight: 600;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .main-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(192, 57, 255, 0.4);
        }

        .main-button:active {
            transform: translateY(0px);
            box-shadow: 0 2px 10px rgba(192, 57, 255, 0.3);
        }

        /* تحسينات خاصة بالجوال */
        @media (max-width: 768px) {
            html, body {
                font-size: 16px;
                padding: 0;
                margin: 0;
                overflow-x: hidden;
                width: 100%;
                max-width: 100vw;
                -webkit-text-size-adjust: 100%;
                -ms-text-size-adjust: 100%;
            }

            .container {
                padding: 0.75rem !important;
                margin: 0 !important;
                max-width: 100% !important;
                width: 100% !important;
            }

            .main-button {
                padding: 1rem 2rem !important;
                font-size: 1rem !important;
                border-radius: 0.75rem !important;
                width: auto !important;
                min-width: 120px !important;
                margin: 0 auto !important;
                font-weight: 600 !important;
            }

            .main-button:active {
                transform: scale(0.97) !important;
            }

            input, textarea {
                font-size: 16px !important;
                padding: 0.875rem !important;
                border-radius: 0.75rem !important;
            }

            /* تحسين التفاعل باللمس */
            button, input, textarea {
                touch-action: manipulation !important;
                -webkit-tap-highlight-color: rgba(192, 57, 255, 0.3) !important;
            }
        }

        /* شاشة التحميل */
        #preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: radial-gradient(circle at 60% 40%, #c039ff22 0%, #6a11cb11 40%, #02041a 100%);
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: opacity 0.7s ease-out;
            opacity: 1;
            pointer-events: auto;
        }

        #preloader.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .preloader-content {
            text-align: center;
            color: white;
        }

        .preloader-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #c039ff, #6a11cb);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .preloader-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .preloader-subtitle {
            font-size: 1rem;
            opacity: 0.8;
        }

        /* تحسينات إضافية للأداء */
        * {
            will-change: auto !important;
        }

        .animate-slide-in,
        .fade-in,
        .scale-in {
            animation-duration: 0.2s !important;
        }

        /* تحسين التمرير للجوال */
        body {
            -webkit-overflow-scrolling: touch !important;
            overscroll-behavior: contain !important;
        }

        /* تحسين الخطوط للجوال */
        body, input, textarea, button {
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }
    </style>
</head>

<body>
    <!-- شاشة التحميل -->
    <div id="preloader">
        <div class="preloader-content">
            <div class="preloader-icon">
                <svg width="40" height="40" viewBox="0 0 100 100" fill="white">
                    <path d="M50,15.2C25.4,15.2,16.5,35.7,18,50.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1C83.5,35.7,74.6,15.2,50,15.2z"/>
                    <circle cx="42" cy="55" r="5" fill="white"/>
                    <circle cx="58" cy="55" r="5" fill="white"/>
                </svg>
            </div>
            <div class="preloader-title">العقل المبدع</div>
            <div class="preloader-subtitle">جاري التحميل...</div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div id="main-app-container" style="opacity: 0;">
        <main class="container mx-auto px-4 py-8 min-h-screen flex flex-col items-center justify-center">
            <!-- العنوان الرئيسي -->
            <div class="text-center mb-8">
                <div class="floating-icon mb-4">
                    <svg width="80" height="80" viewBox="0 0 100 100" class="mx-auto">
                        <path fill="#a78bfa" d="M50,15.2C25.4,15.2,16.5,35.7,18,50.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1C83.5,35.7,74.6,15.2,50,15.2z"/>
                        <circle cx="42" cy="55" r="5" fill="white"/>
                        <circle cx="58" cy="55" r="5" fill="white"/>
                    </svg>
                </div>
                <h1 class="text-4xl font-bold mb-2 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                    العقل المبدع
                </h1>
                <p class="text-lg text-gray-300">منصة شاملة للإبداع والتعلم</p>
            </div>

            <!-- رسالة الترحيب -->
            <div class="glassmorphism p-6 rounded-2xl text-center max-w-md mx-auto">
                <h2 class="text-xl font-semibold mb-4">مرحباً بك في تطبيق العقل المبدع</h2>
                <p class="text-gray-300 mb-6">
                    تطبيق شامل يجمع بين الوصفات، المشاريع، التعليم، الصحة، القصص والمعاملات في مكان واحد
                </p>
                
                <!-- زر البدء -->
                <button onclick="startApp()" class="main-button px-8 py-3 rounded-xl text-white font-semibold">
                    ابدأ الاستكشاف
                </button>
            </div>

            <!-- معلومات التطبيق -->
            <div class="mt-8 text-center text-sm text-gray-400">
                <p>الإصدار 2.0 - تطبيق Android</p>
                <p>تم التطوير بواسطة فريق العقل المبدع</p>
            </div>
        </main>
    </div>

    <script>
        // التحقق من وجود واجهة Android
        const isAndroidApp = typeof Android !== 'undefined';
        
        // إخفاء شاشة التحميل بعد 4 ثوان
        setTimeout(() => {
            const preloader = document.getElementById('preloader');
            const mainContainer = document.getElementById('main-app-container');
            
            preloader.classList.add('hidden');
            mainContainer.style.opacity = '1';
            mainContainer.style.transition = 'opacity 0.8s ease-in';
            
            // إشعار Android بانتهاء التحميل
            if (isAndroidApp) {
                Android.logEvent('app_loaded', 'preloader_finished');
            }
        }, 4000);

        // دالة بدء التطبيق
        function startApp() {
            if (isAndroidApp) {
                Android.vibrate(50);
                Android.showToast('مرحباً بك في العقل المبدع!');
                Android.logEvent('app_started', 'user_clicked_start');
            }

            // تحميل المحتوى الأصلي للتطبيق
            loadOriginalContent();
        }

        // تحميل المحتوى الأصلي
        function loadOriginalContent() {
            if (isAndroidApp) {
                Android.showToast('جاري تحميل التطبيق...');
            }

            // إخفاء شاشة الترحيب وعرض المحتوى الأصلي
            document.getElementById('main-app-container').style.display = 'none';

            // إنشاء المحتوى الأصلي
            const originalContent = document.createElement('div');
            originalContent.innerHTML = getOriginalAppContent();
            document.body.appendChild(originalContent);

            // تهيئة التطبيق الأصلي
            initializeOriginalApp();

            if (isAndroidApp) {
                Android.showToast('تم تحميل التطبيق بنجاح!');
            }
        }

        // الحصول على المحتوى الأصلي للتطبيق
        function getOriginalAppContent() {
            return `
                <!-- المحتوى الأصلي للتطبيق -->
                <div id="particles-js"></div>

                <main class="container mx-auto px-4 py-8 min-h-screen flex flex-col items-center justify-center relative z-10">
                    <!-- العنوان الرئيسي -->
                    <div class="text-center mb-8">
                        <div class="floating-icon mb-4">
                            <svg width="80" height="80" viewBox="0 0 100 100" class="mx-auto">
                                <path fill="#a78bfa" d="M50,15.2C25.4,15.2,16.5,35.7,18,50.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1C83.5,35.7,74.6,15.2,50,15.2z"/>
                                <circle cx="42" cy="55" r="5" fill="white"/>
                                <circle cx="58" cy="55" r="5" fill="white"/>
                            </svg>
                        </div>
                        <h1 class="text-4xl font-bold mb-2 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                            العقل المبدع
                        </h1>
                        <p class="text-lg text-gray-300">منصة شاملة للإبداع والتعلم</p>
                    </div>

                    <!-- شريط البحث -->
                    <div class="w-full max-w-2xl mb-8 relative">
                        <div class="glassmorphism p-4 rounded-2xl">
                            <div class="relative">
                                <input
                                    type="text"
                                    id="searchInput"
                                    placeholder="ابحث عن أي شيء تريد معرفته..."
                                    class="w-full px-6 py-4 bg-transparent border-2 border-purple-500/30 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 text-lg"
                                    autocomplete="off"
                                >
                                <button onclick="performSearch()" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300">
                                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- الاقتراحات التلقائية -->
                        <div id="autoSuggestions" class="absolute top-full left-0 right-0 mt-2 glassmorphism rounded-xl overflow-hidden z-50 hidden">
                        </div>
                    </div>

                    <!-- أقسام التطبيق -->
                    <div id="categorySelector" class="grid grid-cols-3 gap-6 mb-8 w-full max-w-md">
                        <button onclick="selectCategory('recipes')" class="category-btn glassmorphism p-4 rounded-full transition-all duration-300 hover:scale-105 active">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24" class="mx-auto mb-2">
                                <path d="M12,2A3,3 0 0,1 15,5V6.5C15,7.33 14.33,8 13.5,8H10.5C9.67,8 9,7.33 9,6.5V5A3,3 0 0,1 12,2M12,4A1,1 0 0,0 11,5V6H13V5A1,1 0 0,0 12,4M12,8.5L16,12.5V22H8V12.5L12,8.5Z"/>
                            </svg>
                            <span class="text-sm font-semibold">الوصفات</span>
                        </button>

                        <button onclick="selectCategory('projects')" class="category-btn glassmorphism p-4 rounded-full transition-all duration-300 hover:scale-105">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24" class="mx-auto mb-2">
                                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z"/>
                            </svg>
                            <span class="text-sm font-semibold">المشاريع</span>
                        </button>

                        <button onclick="selectCategory('education')" class="category-btn glassmorphism p-4 rounded-full transition-all duration-300 hover:scale-105">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24" class="mx-auto mb-2">
                                <path d="M12,3L1,9L12,15L21,10.09V17H23V9M5,13.18V17.18L12,21L19,17.18V13.18L12,17L5,13.18Z"/>
                            </svg>
                            <span class="text-sm font-semibold">التعليم</span>
                        </button>

                        <button onclick="selectCategory('health')" class="category-btn glassmorphism p-4 rounded-full transition-all duration-300 hover:scale-105">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24" class="mx-auto mb-2">
                                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M17,13H13V17H11V13H7V11H11V7H13V11H17V13Z"/>
                            </svg>
                            <span class="text-sm font-semibold">الصحة</span>
                        </button>

                        <button onclick="selectCategory('stories')" class="category-btn glassmorphism p-4 rounded-full transition-all duration-300 hover:scale-105">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24" class="mx-auto mb-2">
                                <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z"/>
                            </svg>
                            <span class="text-sm font-semibold">القصص</span>
                        </button>

                        <button onclick="selectCategory('dealings')" class="category-btn glassmorphism p-4 rounded-full transition-all duration-300 hover:scale-105">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24" class="mx-auto mb-2">
                                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,17H13V11H11V17M11,9H13V7H11V9Z"/>
                            </svg>
                            <span class="text-sm font-semibold">المعاملات</span>
                        </button>
                    </div>

                    <!-- اقتراحات سريعة -->
                    <div id="suggestionContainer" class="grid grid-cols-2 gap-4 w-full max-w-md mb-8">
                        <button onclick="quickSearch('وصفة كيك')" class="suggestion-btn glassmorphism px-4 py-3 rounded-xl text-sm hover:bg-purple-500/20 transition-all">
                            🍰 وصفة كيك
                        </button>
                        <button onclick="quickSearch('مشروع مربح')" class="suggestion-btn glassmorphism px-4 py-3 rounded-xl text-sm hover:bg-purple-500/20 transition-all">
                            💰 مشروع مربح
                        </button>
                        <button onclick="quickSearch('تعلم البرمجة')" class="suggestion-btn glassmorphism px-4 py-3 rounded-xl text-sm hover:bg-purple-500/20 transition-all col-span-2">
                            💻 تعلم البرمجة
                        </button>
                    </div>

                    <!-- منطقة النتائج -->
                    <div id="resultsContainer" class="w-full max-w-4xl hidden">
                        <div id="loadingIndicator" class="text-center py-8 hidden">
                            <div class="custom-loader mx-auto mb-4"></div>
                            <p class="text-gray-400">جاري البحث...</p>
                        </div>

                        <div id="searchResults" class="space-y-6">
                            <!-- النتائج ستظهر هنا -->
                        </div>
                    </div>
                </main>
            `;
        }

        // تهيئة التطبيق الأصلي
        function initializeOriginalApp() {
            // إضافة مستمعي الأحداث
            setupEventListeners();

            // تهيئة الاقتراحات التلقائية
            initializeAutoSuggestions();

            // تحديد القسم الافتراضي
            selectCategory('recipes');
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', handleSearchInput);
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        performSearch();
                    }
                });
            }
        }

        // معالجة إدخال البحث
        function handleSearchInput(e) {
            const query = e.target.value.trim();
            if (query.length > 2) {
                showAutoSuggestions(query);
            } else {
                hideAutoSuggestions();
            }
        }

        // تهيئة الاقتراحات التلقائية
        function initializeAutoSuggestions() {
            // قائمة الاقتراحات المحددة مسبقاً
            window.suggestions = {
                recipes: [
                    'وصفة كيك الشوكولاتة',
                    'طريقة عمل المعكرونة',
                    'وصفة البيتزا الإيطالية',
                    'حلويات رمضان'
                ],
                projects: [
                    'مشروع تجارة إلكترونية',
                    'مشروع مطعم صغير',
                    'استثمار في العقارات',
                    'مشروع تطبيق جوال'
                ],
                education: [
                    'تعلم البرمجة',
                    'دورة اللغة الإنجليزية',
                    'تعلم التصميم الجرافيكي',
                    'دراسة إدارة الأعمال'
                ],
                health: [
                    'علاج الصداع',
                    'تمارين اللياقة البدنية',
                    'نظام غذائي صحي',
                    'علاج الأرق'
                ],
                stories: [
                    'قصة الأسد والفأر',
                    'حكايات الأطفال',
                    'قصص تاريخية',
                    'قصص ملهمة'
                ],
                dealings: [
                    'آداب التعامل مع الآخرين',
                    'فن التفاوض',
                    'مهارات التواصل',
                    'حل النزاعات'
                ]
            };
        }

        // عرض الاقتراحات التلقائية
        function showAutoSuggestions(query) {
            const suggestionsContainer = document.getElementById('autoSuggestions');
            if (!suggestionsContainer) return;

            const currentCategory = getCurrentCategory();
            const categorySuggestions = window.suggestions[currentCategory] || [];

            const filteredSuggestions = categorySuggestions.filter(suggestion =>
                suggestion.toLowerCase().includes(query.toLowerCase())
            );

            if (filteredSuggestions.length > 0) {
                suggestionsContainer.innerHTML = filteredSuggestions.map(suggestion =>
                    `<div class="suggestion-item" onclick="selectSuggestion('${suggestion}')">${suggestion}</div>`
                ).join('');
                suggestionsContainer.classList.remove('hidden');
            } else {
                hideAutoSuggestions();
            }
        }

        // إخفاء الاقتراحات التلقائية
        function hideAutoSuggestions() {
            const suggestionsContainer = document.getElementById('autoSuggestions');
            if (suggestionsContainer) {
                suggestionsContainer.classList.add('hidden');
            }
        }

        // اختيار اقتراح
        function selectSuggestion(suggestion) {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = suggestion;
                hideAutoSuggestions();
                performSearch();
            }
        }

        // الحصول على القسم الحالي
        function getCurrentCategory() {
            const activeButton = document.querySelector('.category-btn.active');
            if (activeButton) {
                const onclick = activeButton.getAttribute('onclick');
                const match = onclick.match(/selectCategory\('(.+)'\)/);
                return match ? match[1] : 'recipes';
            }
            return 'recipes';
        }

        // اختيار قسم
        function selectCategory(category) {
            // إزالة التحديد من جميع الأزرار
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // تحديد الزر المختار
            const selectedButton = document.querySelector(`[onclick="selectCategory('${category}')"]`);
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            // تحديث الاقتراحات السريعة
            updateQuickSuggestions(category);

            // تسجيل الحدث
            if (isAndroidApp) {
                Android.logEvent('category_selected', category);
            }
        }

        // تحديث الاقتراحات السريعة
        function updateQuickSuggestions(category) {
            const container = document.getElementById('suggestionContainer');
            if (!container) return;

            const suggestions = window.suggestions[category] || [];
            const quickSuggestions = suggestions.slice(0, 3);

            container.innerHTML = quickSuggestions.map((suggestion, index) => {
                const colSpan = index === 2 ? 'col-span-2' : '';
                return `<button onclick="quickSearch('${suggestion}')" class="suggestion-btn glassmorphism px-4 py-3 rounded-xl text-sm hover:bg-purple-500/20 transition-all ${colSpan}">
                    ${getCategoryIcon(category)} ${suggestion}
                </button>`;
            }).join('');
        }

        // الحصول على أيقونة القسم
        function getCategoryIcon(category) {
            const icons = {
                recipes: '🍰',
                projects: '💰',
                education: '💻',
                health: '🏥',
                stories: '📚',
                dealings: '🤝'
            };
            return icons[category] || '✨';
        }

        // بحث سريع
        function quickSearch(query) {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = query;
                performSearch();
            }
        }

        // تنفيذ البحث
        function performSearch() {
            const searchInput = document.getElementById('searchInput');
            if (!searchInput) return;

            const query = searchInput.value.trim();
            if (!query) return;

            // إخفاء الاقتراحات
            hideAutoSuggestions();

            // عرض مؤشر التحميل
            showLoadingIndicator();

            // تسجيل البحث
            if (isAndroidApp) {
                Android.logEvent('search_performed', query);
                Android.vibrate(30);
            }

            // محاكاة البحث
            setTimeout(() => {
                hideLoadingIndicator();
                displaySearchResults(query);
            }, 1500);
        }

        // عرض مؤشر التحميل
        function showLoadingIndicator() {
            const resultsContainer = document.getElementById('resultsContainer');
            const loadingIndicator = document.getElementById('loadingIndicator');

            if (resultsContainer && loadingIndicator) {
                resultsContainer.classList.remove('hidden');
                loadingIndicator.classList.remove('hidden');

                // التمرير إلى النتائج
                resultsContainer.scrollIntoView({ behavior: 'smooth' });
            }
        }

        // إخفاء مؤشر التحميل
        function hideLoadingIndicator() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.classList.add('hidden');
            }
        }

        // عرض نتائج البحث
        function displaySearchResults(query) {
            const searchResults = document.getElementById('searchResults');
            if (!searchResults) return;

            const category = getCurrentCategory();
            const results = generateSearchResults(query, category);

            searchResults.innerHTML = results.map(result => createResultCard(result)).join('');

            // إضافة تأثيرات الظهور
            setTimeout(() => {
                searchResults.querySelectorAll('.idea-card').forEach((card, index) => {
                    setTimeout(() => {
                        card.classList.add('animate-slide-in');
                    }, index * 100);
                });
            }, 100);
        }

        // إنشاء نتائج البحث
        function generateSearchResults(query, category) {
            const results = [];
            const maxResults = 4; // الحد الأقصى للنتائج

            for (let i = 0; i < maxResults; i++) {
                results.push({
                    id: `result_${i}`,
                    title: generateResultTitle(query, category, i),
                    content: generateResultContent(query, category, i),
                    category: category,
                    type: getResultType(category)
                });
            }

            return results;
        }

        // إنشاء عنوان النتيجة
        function generateResultTitle(query, category, index) {
            const titles = {
                recipes: [
                    `وصفة ${query} الشهية`,
                    `طريقة تحضير ${query} بسهولة`,
                    `${query} بطريقة احترافية`,
                    `وصفة ${query} التقليدية`
                ],
                projects: [
                    `مشروع ${query} المربح`,
                    `كيفية بدء ${query}`,
                    `دراسة جدوى ${query}`,
                    `نصائح لنجاح ${query}`
                ],
                education: [
                    `دليل تعلم ${query}`,
                    `كورس ${query} المتكامل`,
                    `أساسيات ${query}`,
                    `مهارات ${query} المتقدمة`
                ],
                health: [
                    `علاج ${query} الطبيعي`,
                    `الوقاية من ${query}`,
                    `نصائح للتعامل مع ${query}`,
                    `العلاج المنزلي لـ ${query}`
                ],
                stories: [
                    `قصة ${query} الملهمة`,
                    `حكاية ${query} الشيقة`,
                    `قصة ${query} التعليمية`,
                    `حكاية ${query} الممتعة`
                ],
                dealings: [
                    `فن التعامل في ${query}`,
                    `آداب ${query}`,
                    `مهارات ${query}`,
                    `استراتيجيات ${query}`
                ]
            };

            return titles[category] ? titles[category][index] : `${query} - نتيجة ${index + 1}`;
        }

        // إنشاء محتوى النتيجة
        function generateResultContent(query, category, index) {
            const contents = {
                recipes: [
                    `مقادير وطريقة تحضير ${query} بخطوات واضحة ومبسطة. وصفة مجربة ومضمونة النتائج.`,
                    `تعلم كيفية إعداد ${query} بطريقة سهلة وسريعة مع نصائح الطهاة المحترفين.`,
                    `وصفة ${query} الأصلية مع الأسرار والحيل لنتيجة مثالية في كل مرة.`,
                    `طريقة تحضير ${query} التقليدية مع لمسة عصرية ومكونات متوفرة.`
                ],
                projects: [
                    `دليل شامل لبدء مشروع ${query} مع دراسة السوق والتكاليف المتوقعة.`,
                    `خطة عمل متكاملة لمشروع ${query} مع نصائح للنجاح وتجنب المخاطر.`,
                    `كيفية تمويل وإدارة مشروع ${query} بطريقة احترافية ومربحة.`,
                    `أفكار إبداعية لتطوير مشروع ${query} وزيادة الأرباح.`
                ],
                education: [
                    `منهج تعليمي متدرج لإتقان ${query} من البداية حتى الاحتراف.`,
                    `دورة تدريبية شاملة في ${query} مع تطبيقات عملية ومشاريع.`,
                    `أساسيات ${query} المهمة مع شرح مبسط وأمثلة واقعية.`,
                    `تقنيات متقدمة في ${query} للوصول إلى مستوى الخبراء.`
                ],
                health: [
                    `طرق علاج ${query} الطبيعية والآمنة مع نصائح الأطباء المختصين.`,
                    `برنامج وقائي شامل للحماية من ${query} ونصائح للحياة الصحية.`,
                    `علاجات منزلية مجربة لـ ${query} مع المكونات الطبيعية.`,
                    `دليل التعامل مع ${query} ونصائح للتعافي السريع.`
                ],
                stories: [
                    `قصة ملهمة عن ${query} تحمل دروساً قيمة ومعاني عميقة.`,
                    `حكاية شيقة حول ${query} مليئة بالأحداث المثيرة والعبر.`,
                    `قصة تعليمية عن ${query} تناسب جميع الأعمار وتحمل فوائد تربوية.`,
                    `حكاية ممتعة حول ${query} تجمع بين التسلية والفائدة.`
                ],
                dealings: [
                    `أساليب راقية للتعامل في ${query} مع احترام الآخرين وتحقيق النتائج.`,
                    `آداب وأخلاقيات ${query} في الثقافة العربية والإسلامية.`,
                    `مهارات التواصل الفعال في ${query} مع تطبيقات عملية.`,
                    `استراتيجيات ذكية للنجاح في ${query} وبناء علاقات إيجابية.`
                ]
            };

            return contents[category] ? contents[category][index] : `محتوى مفيد حول ${query} يساعدك في تحقيق أهدافك.`;
        }

        // الحصول على نوع النتيجة
        function getResultType(category) {
            const types = {
                recipes: 'recipe',
                projects: 'project',
                education: 'educational',
                health: 'health',
                stories: 'story',
                dealings: 'dealings'
            };
            return types[category] || 'general';
        }

        // إنشاء بطاقة النتيجة
        function createResultCard(result) {
            const categoryColors = {
                recipes: 'border-purple-500',
                projects: 'border-green-500',
                education: 'border-blue-500',
                health: 'border-green-500',
                stories: 'border-pink-500',
                dealings: 'border-purple-500'
            };

            const borderColor = categoryColors[result.category] || 'border-purple-500';

            return `
                <div class="idea-card glassmorphism p-6 rounded-2xl ${borderColor} border-r-4 relative group">
                    <!-- زر النسخ -->
                    <button onclick="copyResult('${result.id}')" class="copy-btn absolute top-4 left-4 p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                        </svg>
                    </button>

                    <!-- شريط القسم -->
                    <div class="absolute top-0 right-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-t-2xl"></div>

                    <!-- المحتوى -->
                    <div class="${result.type}-content">
                        <h3 class="text-xl font-bold mb-4 text-white">${result.title}</h3>
                        <p class="text-gray-300 leading-relaxed mb-4">${result.content}</p>

                        <!-- زر التوسيع -->
                        <button onclick="expandResult('${result.id}')" class="expand-btn main-button px-6 py-2 rounded-lg text-sm font-semibold">
                            عرض التفاصيل
                        </button>
                    </div>
                </div>
            `;
        }

        // نسخ النتيجة
        function copyResult(resultId) {
            const resultCard = document.querySelector(`[onclick="expandResult('${resultId}')"]`).closest('.idea-card');
            const title = resultCard.querySelector('h3').textContent;
            const content = resultCard.querySelector('p').textContent;
            const textToCopy = `${title}\n\n${content}`;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(textToCopy).then(() => {
                    if (isAndroidApp) {
                        Android.showToast('تم نسخ المحتوى');
                        Android.vibrate(50);
                    }
                });
            } else if (isAndroidApp) {
                Android.showToast('تم نسخ المحتوى');
                Android.vibrate(50);
            }
        }

        // توسيع النتيجة
        function expandResult(resultId) {
            if (isAndroidApp) {
                Android.vibrate(30);
                Android.showToast('عرض التفاصيل الكاملة');
                Android.logEvent('result_expanded', resultId);
            }

            // هنا يمكن إضافة منطق عرض التفاصيل الكاملة
            // مثل فتح نافذة منبثقة أو الانتقال إلى صفحة جديدة
        }

        // التعامل مع أحداث Android
        if (isAndroidApp) {
            // حفظ حالة التطبيق
            window.addEventListener('beforeunload', () => {
                Android.saveData('last_visit', new Date().toISOString());
            });
            
            // تسجيل بدء الجلسة
            Android.logEvent('session_start', 'app_opened');
            
            // التحقق من الاتصال بالإنترنت
            if (!Android.isNetworkAvailable()) {
                Android.showToast('تحذير: لا يوجد اتصال بالإنترنت');
            }
        }

        // تحسين الأداء للجوال
        document.addEventListener('DOMContentLoaded', () => {
            // تحسين التمرير
            document.body.style.webkitOverflowScrolling = 'touch';
            
            // منع التكبير عند النقر المزدوج
            let lastTouchEnd = 0;
            document.addEventListener('touchend', (event) => {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);
        });
    </script>
</body>
</html>
