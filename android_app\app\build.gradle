plugins {
    id 'com.android.application'
}

android {
    namespace 'com.alaqalmubdea.app'
    compileSdk 34

    defaultConfig {
        applicationId "com.alaqalmubdea.app"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "2.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // دعم اللغة العربية
        resConfigs "ar", "en"
        
        // تحسين الأداء
        multiDexEnabled true
        
        // إعدادات ProGuard
        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // تحسين الأداء
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            
            // إعدادات التوقيع (يجب إضافة keystore)
            // signingConfig signingConfigs.release
        }
        
        debug {
            minifyEnabled false
            debuggable true
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
    }
    
    // إعدادات الضغط
    packagingOptions {
        resources {
            excludes += ['META-INF/DEPENDENCIES', 'META-INF/LICENSE', 'META-INF/LICENSE.txt', 'META-INF/license.txt', 'META-INF/NOTICE', 'META-INF/NOTICE.txt', 'META-INF/notice.txt', 'META-INF/ASL2.0']
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    // تحسين البناء
    dexOptions {
        javaMaxHeapSize "4g"
        preDexLibraries = false
    }
    
    // إعدادات Lint
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
        disable 'MissingTranslation'
    }
    
    // دعم View Binding
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    // مكتبات Android الأساسية
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.activity:activity:1.8.1'
    implementation 'androidx.fragment:fragment:1.6.2'
    
    // WebView المحسن
    implementation 'androidx.webkit:webkit:1.8.0'
    
    // SwipeRefreshLayout
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    
    // مكتبات الشبكة
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // مكتبات الصور
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'
    
    // مكتبات الأذونات
    implementation 'com.karumi:dexter:6.2.3'
    
    // مكتبات التخزين المحلي
    implementation 'androidx.preference:preference:1.2.1'
    implementation 'androidx.room:room-runtime:2.6.0'
    annotationProcessor 'androidx.room:room-compiler:2.6.0'
    
    // مكتبات الأمان
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'
    
    // مكتبات التحليلات (اختيارية)
    // implementation 'com.google.firebase:firebase-analytics:21.5.0'
    // implementation 'com.google.firebase:firebase-crashlytics:18.6.0'
    
    // مكتبات الإشعارات
    implementation 'androidx.work:work-runtime:2.8.1'
    
    // مكتبات التنقل
    implementation 'androidx.navigation:navigation-fragment:2.7.5'
    implementation 'androidx.navigation:navigation-ui:2.7.5'
    
    // مكتبات الحياة الدورية
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.7.0'
    
    // مكتبات الاختبار
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    
    // مكتبات إضافية للتحسين
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.vectordrawable:vectordrawable:1.1.0'
    implementation 'androidx.vectordrawable:vectordrawable-animated:1.1.0'
    
    // مكتبات الخطوط
    implementation 'androidx.core:core-ktx:1.12.0'
    
    // مكتبات الضغط والتحسين
    implementation 'com.facebook.fresco:fresco:3.1.3'
    implementation 'com.facebook.fresco:animated-gif:3.1.3'
    
    // مكتبات JSON
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // مكتبات التاريخ والوقت
    implementation 'com.jakewharton.threetenabp:threetenabp:1.4.6'
    
    // مكتبات الملفات
    implementation 'commons-io:commons-io:2.11.0'
    
    // مكتبات التشفير
    implementation 'org.bouncycastle:bcprov-jdk15on:1.70'
}

// إعدادات إضافية للتحسين
android.applicationVariants.all { variant ->
    variant.outputs.all {
        def appName = "AlAqlAlMubdea"
        def versionName = variant.versionName
        def versionCode = variant.versionCode
        def buildType = variant.buildType.name
        def date = new Date().format('yyyyMMdd')
        
        outputFileName = "${appName}_v${versionName}_${buildType}_${date}.apk"
    }
}
