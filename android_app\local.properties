# This file contains machine-specific properties for the Android SDK.
# This file should NOT be checked into version control.
# Location of the Android SDK. This is only used by Gradle.
# For customization when using a Version Control System, please read the
# header note.

# Android SDK location (update this path according to your system)
# Windows example:
# sdk.dir=C\:\\Users\\YourUsername\\AppData\\Local\\Android\\Sdk

# macOS/Linux example:
# sdk.dir=/Users/<USER>/Library/Android/sdk

# You need to set this to your actual Android SDK path
# Update this path to match your Android SDK installation
sdk.dir=C\:\\Users\\ThinkPad\\AppData\\Local\\Android\\Sdk

# NDK location (if using NDK)
# ndk.dir=C\:\\Users\\YourUsername\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393

# CMake location (if using CMake)
# cmake.dir=C\:\\Users\\YourUsername\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1
