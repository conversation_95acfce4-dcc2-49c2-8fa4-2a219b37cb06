#!/bin/bash

echo "========================================"
echo "   بناء تطبيق العقل المبدع - Android APK"
echo "========================================"
echo

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة لطباعة رسائل ملونة
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# التحقق من وجود Java
if ! command -v java &> /dev/null; then
    print_error "Java غير مثبت أو غير موجود في PATH"
    print_info "يرجى تثبيت Java 8 أو أحدث"
    exit 1
fi

print_success "Java متوفر"

# التحقق من وجود Android SDK
if [ ! -f "local.properties" ]; then
    print_warning "ملف local.properties غير موجود"
    print_info "يرجى إنشاء الملف وتحديد مسار Android SDK"
    print_info "مثال: sdk.dir=/Users/<USER>/Library/Android/sdk"
    echo
fi

# جعل gradlew قابل للتنفيذ
chmod +x gradlew

print_info "جاري تنظيف المشروع..."
./gradlew clean
if [ $? -ne 0 ]; then
    print_error "خطأ في تنظيف المشروع"
    exit 1
fi
print_success "تم تنظيف المشروع"

echo
print_info "جاري بناء إصدار التطوير (Debug)..."
./gradlew assembleDebug
if [ $? -ne 0 ]; then
    print_error "خطأ في بناء إصدار التطوير"
    exit 1
fi
print_success "تم بناء إصدار التطوير"

echo
print_info "جاري بناء إصدار الإنتاج (Release)..."
./gradlew assembleRelease
if [ $? -ne 0 ]; then
    print_error "خطأ في بناء إصدار الإنتاج"
    exit 1
fi
print_success "تم بناء إصدار الإنتاج"

echo
echo "========================================"
print_success "           تم البناء بنجاح!"
echo "========================================"
echo

# عرض مواقع ملفات APK
print_info "ملفات APK المُنشأة:"
echo

if ls app/build/outputs/apk/debug/*.apk 1> /dev/null 2>&1; then
    echo "إصدار التطوير (Debug):"
    for file in app/build/outputs/apk/debug/*.apk; do
        echo "  $file"
    done
    echo
fi

if ls app/build/outputs/apk/release/*.apk 1> /dev/null 2>&1; then
    echo "إصدار الإنتاج (Release):"
    for file in app/build/outputs/apk/release/*.apk; do
        echo "  $file"
    done
    echo
fi

# إنشاء مجلد للملفات النهائية
mkdir -p output

# نسخ ملفات APK إلى مجلد output
print_info "جاري نسخ ملفات APK إلى مجلد output..."
if ls app/build/outputs/apk/debug/*.apk 1> /dev/null 2>&1; then
    cp app/build/outputs/apk/debug/*.apk output/
fi
if ls app/build/outputs/apk/release/*.apk 1> /dev/null 2>&1; then
    cp app/build/outputs/apk/release/*.apk output/
fi

print_success "تم نسخ جميع ملفات APK إلى مجلد: output/"
echo

# عرض معلومات إضافية
print_info "معلومات إضافية:"
echo "- لتثبيت التطبيق على الجهاز: adb install output/[اسم_الملف].apk"
echo "- لتوقيع APK للنشر: استخدم Android Studio أو jarsigner"
echo "- لتحسين الحجم: تأكد من تفعيل ProGuard في إصدار الإنتاج"
echo

# عرض حجم الملفات
print_info "أحجام ملفات APK:"
for file in output/*.apk; do
    if [ -f "$file" ]; then
        size=$(du -h "$file" | cut -f1)
        basename_file=$(basename "$file")
        echo "  $basename_file: $size"
    fi
done
echo

echo "========================================"
print_success "        تم الانتهاء من جميع العمليات"
echo "========================================"
echo

# فتح مجلد output (للأنظمة التي تدعم ذلك)
if command -v open &> /dev/null; then
    echo "هل تريد فتح مجلد الملفات النهائية؟ (y/n)"
    read -r choice
    if [[ $choice == "y" || $choice == "Y" ]]; then
        open output
    fi
elif command -v xdg-open &> /dev/null; then
    echo "هل تريد فتح مجلد الملفات النهائية؟ (y/n)"
    read -r choice
    if [[ $choice == "y" || $choice == "Y" ]]; then
        xdg-open output
    fi
fi

print_success "تم الانتهاء!"
